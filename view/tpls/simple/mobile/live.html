{extend name="public/mobile_base"}
{block name="header_sub"}
<nav class="bg-white py-3 shadow-sm">
    <div class="nav-scroll-container overflow-x-auto scrollbar-hide">
        <ul class="flex whitespace-nowrap px-4 space-x-6">
            {volist name="match_class" id='m'}
            {php}
                $is_select = 'text-gray-600';
                if($m['is_select']){
                    $is_select = 'border-b-2 border-red-500 pb-1';
                    $m['en_name']!='all' && $mat_name = $m['mat_name'];
                }
            {/php}
            <li class="font-medium {$is_select|raw}"><a href="{$path}/{$m.en_name}/" >{$m.mat_name}</a></li>
            {/volist}
        </ul>
    </div>
</nav>
{/block}
{block name="main"}
<!-- 主要内容区域 -->
<main class="flex-grow p-3 overflow-y-auto" id="live-container">
    <div class="flex space-x-2 overflow-x-auto py-2 scrollbar-hide" id="tabs-title">
        <span class="px-3 py-1 bg-red-500 text-white rounded-full text-sm">{:lang("hot")}</span>
        <span class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm">{:lang("football")}</span>
        <span class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm">{:lang("basketball")}</span>
    </div>
    <!-- 直播列表 -->
    <div class="grid grid-cols-1 gap-4 mt-6" id="tabs-content">
        <!-- 比赛卡片 -->
        <div class="live-item active">
            {volist name="list" id="d"}
                <div class="text-center text-xs text-gray-400 mb-2">   {$d.day} {$d.week} </div>
                {volist name="$d['list']" id="vo"}
                    {include file="public/live-item"}
                {/volist}
            {/volist}
            <!-- 加载更多 -->
            <div class="flex justify-center items-center py-4 hidden">
                <div class="loader ease-linear rounded-full border-4 border-t-4 border-gray-200 h-8 w-8"></div>
                <span class="ml-2 text-gray-500">{:lang("loading")}</span>
            </div>
        </div>
        <div class="live-item hidden">
            {volist name="old_list" id="d"}
                <div class="text-center text-xs text-gray-400 mb-2"> {$d.day} {$d.week} </div>
                {volist name="$d['list']" id="vo"}
                    {include file="public/live-item"}
                {/volist}
            {/volist}
            <div  class="flex justify-center items-center py-4 hidden">
                <div class="loader ease-linear rounded-full border-4 border-t-4 border-gray-200 h-8 w-8"></div>
                <span class="ml-2 text-gray-500">{:lang("loading")}</span>
            </div>
        </div>

    </div>
</main>
{/block}
{block name="js"}
<!-- JavaScript -->
<script>
    $(document).ready(function() {
        // 模拟数据
        const liveData = [
            {
                id:1, title: "英超 VS 曼城", host: "世界杯",
                "team_A_name": "Morocco",
                "team_A_logo": "http:\/\/www.jala-live.com\/static\/img\/20250326\/4c6bca393326a2fb6e7672519f0c7e54.jpg",
                "team_B_name": "Tanzania",
                "team_B_logo": "http:\/\/www.jala-live.com\/static\/img\/20250326\/5e2ae541b1dee56fb9e17b62ed95a5b3.jpg",
                "match_name": "FIFA World Cup qualification (CAF)",
                "match_type": "足球",
                "fs_A": 0,
                "fs_B": 0,
                "start_time":"05:30","live_status": "Processing","detail_path": "\/zuqiu\/zhibo\/419455.html",
            },
        ];

        let page = 1;
        let loading = false;

        // 导航栏滑动效果
        $('.nav-scroll-container li').click(function() {
            $('.nav-scroll-container li').removeClass('text-red-500 border-b-2 border-red-500').addClass('text-gray-600');
            $(this).removeClass('text-gray-600').addClass('text-red-500 border-b-2 border-red-500');
        });


        // 选项卡
        const tabTitles = $('#tabs-title span');
        const tabContents = $('.live-item');
        // Initialize - hide all content except first one
        tabContents.not(':first').addClass('hidden');
        // Add click event handler to tab titles
        tabTitles.click(function() {
            // Remove active class from all tabs
            tabTitles.removeClass('bg-red-500 text-white').addClass('bg-gray-100 text-gray-600');

            // Add active class to clicked tab
            $(this).removeClass('bg-gray-100 text-gray-600').addClass('bg-red-500 text-white');

            // Get the index of clicked tab
            const index = $(this).index();

            // Hide all content
            tabContents.addClass('hidden');

            // Show corresponding content
            tabContents.eq(index).removeClass('hidden');
        });


        // 加载更多数据函数
        function loadMoreData() {
            if (loading) return;

            loading = true;
            $('#loader').removeClass('hidden');

            // 模拟网络请求延迟
            setTimeout(function() {
                // 添加新的直播项
                let html = '';

                liveData.forEach(function(item) {
                    html += `

                        `;
                });

                $('#live-list').append(html);
                $('#loader').addClass('hidden');
                loading = false;
                page++;

                // 如果加载了3页以上，模拟没有更多数据了
                if (page > 3) {
                    $(window).off('scroll');
                    $('#loader').removeClass('hidden').html('<span class="text-gray-400">没有更多数据了</span>');
                }
            }, 1500);
        }
        // 监听滚动事件，实现底部加载更多
        $(window).scroll(function() {
            // 修改触发距离的计算，考虑固定footer的高度
            const footerHeight = $('footer').outerHeight();
            const scrollPosition = $(window).scrollTop() + $(window).height();
            const triggerPosition = $(document).height() - footerHeight - 50; // 减去footer高度和一个缓冲距离

            if(scrollPosition > triggerPosition && !loading) {
                loadMoreData();
            }
        });
    });
</script>
{/block}

{extend name="public/base"}
{block name="main"}
<main class="flex-grow py-6 px-4 overflow-y-auto container mx-auto" id="live-container">
    <!-- 轮播图 -->
    <div class="rounded-lg overflow-hidden mb-6 relative">
        <div class="nav-scroll-container   scrollbar-hide">
            <ul class="flex whitespace-nowrap px-4 space-x-6">
                {volist name="match_class" id='m'}
                {php}
                $is_select = 'text-gray-600';
                if($m['is_select']){
                $is_select = 'border-b-2 border-red-500 pb-1';
                $m['en_name']!='all' && $mat_name = $m['mat_name'];
                }
                {/php}
                <li class="font-medium {$is_select|raw}"><a href="{$path}/{$m.en_name}/" >{$m.mat_name}</a></li>
                {/volist}
            </ul>
        </div>
    </div>

    <div class="container mx-auto px-4">
        <div class="flex flex-col md:flex-row gap-6" id="main-content">
            <div id="live-content" class="flex-1 w-full md:w-2/3">
                <!--直播推荐-->
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl md:text-2xl font-bold">热门直播</h2>
                    <div class="flex space-x-2  py-2 scrollbar-hide" id="tabs-title">
                        {volist name="match_class" id='m'}
                        {php}
                        $is_select = 'bg-gray-100 text-gray-600';
                        if($m['is_select']){
                        $is_select = 'bg-red-500 text-white';
                        $m['en_name']!='all' && $mat_name = $m['mat_name'];
                        }
                        {/php}
                        <a class="px-3 py-1 cursor-pointer {$is_select|raw} text-gray-600  text-sm" href="{$path}/{$m.en_name}/"> {$m.mat_name} </a>
                        {/volist}
                    </div>
                </div>
                <!-- 直播列表 -->
                <div class="grid grid-cols-1 gap-4" id="tabs-content">

                    <div class="live-item active">
                        {volist name="list" id="d"}
                        <div class="text-center text-xs text-gray-400 mb-2">   {$d.day} {$d.week} </div>
                        {volist name="$d['list']" id="vo"}
                        {include file="public/live-item"}
                        {/volist}
                        {/volist}
                        <!-- 加载更多 -->
                        <div class="flex justify-center items-center py-4 hidden">
                            <div class="loader ease-linear rounded-full border-4 border-t-4 border-gray-200 h-8 w-8"></div>
                            <span class="ml-2 text-gray-500">{:lang("loading")}</span>
                        </div>
                    </div>

                </div>


            </div>
            <aside id="article-rec" class="w-full md:w-1/3">
                <!--推荐文章-->

                <div class="article-hot">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl md:text-2xl font-bold">推荐文章</h2>
                        <a href="#" class="text-sm text-gray-500 hover:underline">更多</a>
                    </div>

                    <div class="grid grid-cols-1 gap-4 md:gap-6" id="article-list">
                        <div class="bg-white rounded-lg overflow-hidden shadow-sm p-4">
                            <div class="flex space-x-3">
                                <img src="https://jala-live.com/static/img/20250224/ea1fc07b2b8f79cd543e4b0761f7b7eb.jpg" alt="文章封面" class="w-24 h-24 object-cover rounded">
                                <div class="flex-1">
                                    <h3 class="text-base font-medium text-gray-900 line-clamp-2">NBA：湖人击败勇士，詹姆斯砍下三双</h3>
                                    <p class="text-gray-500 text-sm mt-1 line-clamp-2">詹姆斯贡献28分12篮板10助攻，带领球队获胜</p>
                                    <div class="flex items-center justify-between mt-2">
                                        <span class="text-xs text-gray-400">3小时前</span>
                                        <span class="text-xs text-red-500">阅读 1.8万</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

            </aside>
        </div>
    </div>

</main>
{/block}
{block name="js"}
<script>
    // 选项卡
    const tabTitles = $('#tabs-title span');
    const tabContents = $('#live-content .live-item');
    // Initialize - hide all content except first one
    tabContents.not(':first').addClass('hidden');
    // Add click event handler to tab titles
    tabTitles.click(function() {
        // Remove active class from all tabs
        tabTitles.removeClass('bg-red-500 text-white').addClass('bg-gray-100 text-gray-600');
        // Add active class to clicked tab
        $(this).removeClass('bg-gray-100 text-gray-600').addClass('bg-red-500 text-white');
        // Get the index of clicked tab
        const index = $(this).index();
        // Hide all content
        tabContents.addClass('hidden');
        console.warn(tabContents.eq(index))
        // Show corresponding content
        tabContents.eq(index).removeClass('hidden');
    });
</script>
{/block}
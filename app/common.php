<?php
/**
 * Here is your custom functions.
 */

define("DS", DIRECTORY_SEPARATOR);


function conf_website($key = null,$default = '',$file=null)
{
    if ($file){
        $config_file = config_path("website").DIRECTORY_SEPARATOR.$file.".php";
    }else{
        if(!getTopHost()){
            return "";
        }
        
        $config_file = config_path("website").DIRECTORY_SEPARATOR.getTopHost().".php";
    }
    if (!file_exists($config_file)) {
        $system_config = get_system_config();
        if ($system_config) {
            set_website_config($system_config, getTopHost());
        }
    }
    $config = include($config_file);
//    $config = config('website.'.($file?:getTopHost()));
    if ($key) {
        return $config[$key] ?? $default;
    }
    return $config;
}

function getTopHost($url = '',$is_rpc=true): string
{
    try {
        if (!$url){
            if (PHP_SAPI == 'cli'){
                return '';
            }else{
                $url =  request()->domain();
            }
            if (preg_match('/\b(?:\d{1,3}\.){3}\d{1,3}\b/', $url)){
                return $url;
            }
        }
        $url = strtolower($url);
        if (!in_str($url,['http'])){
            $url = "https://$url";
        }
       if (!preg_match('/^http(s)?:\/\/(\w+\.)?[\w-]{1,62}\.[\w]{1,62}(\/)?/', $url)) {
            return '';//域名格式错误
        }
        $hosts = parse_url($url);
        $host = $hosts['host'] ?? "";
        $data = explode('.', $host);
        $n = count($data);
        $preg = '/[\w].+\.(com|net|org|gov|edu)\.cn$/';
        if (($n > 2) && preg_match($preg, $host)) {
            $host = $data[$n - 3] . '.' . $data[$n - 2] . '.' . $data[$n - 1];
        } else {
            $host = $data[$n - 2] . '.' . $data[$n - 1];
        }
        if ($is_rpc){
            $host = str_replace([".","-"],"_",$host);
        }
        return $host;
    }catch (Exception $exception){
        dump("getTopHost : ".$exception->getMessage());exit();
    }
    return "";
}

function get_route($route_name = '', $db = '')
{
    if ($db) {
        $route_list = \think\facade\Db::query(" select id,route_name,curr_path,optimize_path from {$db}.ft_route");
    } else {
        // cache("route_cache_".getTopHost(),null);
        $route_list = db("ft_route")->field("id,route_name,curr_path,optimize_path")
        ->cache("route_cache_".getTopHost(), 86400,"ROUTE_TAG")
        ->select();
    }
    $route = [];
    foreach ($route_list as $item) {
        $route[$item['route_name']] = $item['optimize_path'] ?: $item['curr_path'];
    }

    if ($route_name) {
        $route = @$route[$route_name];
    }
    return $route;
}

function hasMore($list = null)
{
    if ($list['hasMore'] ?? false) {
        return $list['hasMore'];
    }

    if (empty($list) or !property_exists($list, 'lastPage')) {
        return false;
    }
    return request()->request("page", 1) < $list->lastPage();
}

function isMobile(): bool
{
    if (input('tpl')){
        return  true;
    }
    $ua = request()->header('user-agent');
    if (preg_match('/(iPhone|Android)/i', $ua)) {
        return true;
    }
    return false;
}

function db($name,$model='mysql'){
    $force = false;
    if($model == 'mysql' && !in_array($name,['ft_webcast',"ft_site",'ft_live_tags','ft_webcast_history','ft_tags']) and PHP_SAPI!='cli'){
        $model = getTopHost();
    }
    return \think\facade\Db::connect($model, $force)->name($name);
}

function db_main($name){
    return \think\facade\Db::connect('mysql', false)->name($name);
}

function has_db($db_name)
{
    return \think\facade\Db::query("select count(*)  as ct from information_schema.SCHEMATA where SCHEMA_NAME = '{$db_name}'")[0]['ct'] > 0;
}

if (!function_exists('datetime')) {
    /**
     * 将时间戳转换为日期时间
     * @param int $time 时间戳
     * @param string $format 日期时间格式
     * @return string
     */
    function datetime($time, $format = 'Y-m-d H:i:s')
    {
        $time = is_numeric($time) ? $time : strtotime($time);
        if ($time){
            return date($format, $time);
        }
        return  "-";
    }

}

function play_status($start_play,$end_play=0)
{
    $time = time();
    if (!$end_play){
        $end_play = strtotime("+80 minutes");
    }
    if ($end_play < $time) {
        return "Akhir permainan";
    }
    if ($start_play <= $time and $end_play > $time) {
        return "Aliran langsung";
    }
    return "Belum";
}


function auto_url($item, $is_full = false, $route = [])
{
    if (empty($item['id'])) {
        return "";
    }
    $domain = "";
    if ($is_full) {
        $domain = is_string($is_full) ? $is_full : request()->domain();
    }
    $route = $route ?: get_route();
    if (isset($item['team_A_name'])) {
        $dir = $route['play'];
        $cat = $item['match_cat_en_name']??"";
    } else {
        $dir = $route['detail'];
        if (isset($item['is_video']) and $item['is_video'] == 1) {
            $dir = $route['video'];
        }
        $cat = $item['cat_en_name']??"";
    }
    $rep = [
        "<cat>" => $cat,
    ];
    $dir = str_ireplace(array_keys($rep), array_values($rep), $dir);
    return "$domain/$dir/{$item['id']}.html";
}


/**
 * Imagick封装图片压缩裁剪
 */
function compress_img($imgSrc, $imgDst, $percent = ['w'=>100,'h'=>100])
{
    try{
        $imageInfo = getimagesize($imgSrc);
        if (!$imageInfo) {
            return;
        }
        $srcImg = new \Imagick(realpath($imgSrc));

        $width = $srcImg->getImageWidth();
        $height = $srcImg->getImageHeight();

        $newWidth = $percent['w'];
        $newHeight = $percent['h'];

        $is_need_cut = $width > $newWidth || $height > $newHeight;
        if (!$is_need_cut) {
            return;
        }
        if ($width>$newWidth){
            $newHeight = intval($newWidth * $height / $width);
        }
        if ($newHeight>$percent['h'] ){
            $newWidth = intval($newHeight * $width / $height);
        }
        $srcImg->thumbnailImage($newWidth, $newHeight, true,true);
        $srcImg->writeImage($imgDst);
        $srcImg->setImageCompressionQuality(80);
        $srcImg->clear();
        $srcImg->destroy();

    } catch (\Exception $e) {
        return false;
    }
}

function get_jf_class()
{
    return [
        17139 => "Liga Premier", 17133 => "La Liga", 17179 => "Serie A", 17051 => "Bundesliga",
        16786 => "Prancis A", 17258 => "B Barat", 17290 => "Italia B", 17022 => "Prancis B",
        17052 => "Divisi Kedua Jerman", 17195 => "Kejuaraan Inggris", 16741 => "Edlinium", 16778 => "Su Chao",
        17153 => "Super Portugis", 17142 => "Super Rusia", 17155 => "Super Turki", 17659 => "Super Dan",
        17722 => "Liga Super Norwegia", 17758 => "Liga Super Tiongkok",
    ];
}



function to_ft($txt)
{
    $is = conf_website("site_is_traditional");
    if ($is) {
        return \app\service\HanziConvert::convert($txt, true);
    }
    return $txt;
}


function getWeek($time)
{
    $week_array =["Minggu", "Senin", "Selasa", "Rabu", "Kamis", "Jumat", "Sabtu"];;
    return $week_array[date("w", $time)];
}



function get_continue_days($days = 7, $ds = '+', $time = '', $format = 'Y-m-d')
{
    $time = $time !== '' ? $time : time();
    //组合数据
    $date = [];
    if ($ds == '-') {
        for ($i = 1; $i <= $days; $i++) {
            $day = '+' . ($i - $days);
            $date[] = date($format, strtotime($day . ' days', $time));
        }
    } else {
        for ($i = 0; $i < $days; $i++) {
            $date[] = date($format, strtotime($i . ' days', $time));
        }
    }

    return $date;
}





function set_website_config($data,$file_name){
    if(!$file_name){
        return;
    }
    $file = config_path('website') . '/'.$file_name.'.php';
    $content = "<?php \n return [";
    foreach ($data as $k=>$v){
        if (is_array($v)){
            $content .= "\n    '$k' => [";
            foreach ($v as $k1=>$v1){
                $content .= "\n        '$k1' => '$v1',";
            }
            $content .= "\n        ],";
        }else{
            $content .= "\n    '$k' => '$v',";
        }
    }
    $content .= "\n];";
    file_put_contents($file,$content);
}



function get_match_class(&$data, $path, $code = 'list')
{
    $data["match_class"] = [["mat_name" => lang('rec'), "en_name" => 'list', "is_select" => !$code or $code == "list"]];
    $ll = match_class_zdy($path, $code ?: "list");
    foreach ($ll as $item) {
        $data["match_class"][] = $item;
    }
}

function match_class_zdy($cat_en_name = '', $en_name = '')
{
    if (in_array($cat_en_name, ['lanqiu', "zuqiu"])) {
        if ($cat_en_name == 'lanqiu') {
            // $cat_names = ["NBA", "CBA", "勇士", "篮网", "湖人", "76人",];
            $cat_names = ["NBA", "CBA", "Warriors", "Nets", "Lakers", "Basketball Highlights",];
        } else {
            // $cat_names = ['英超', '意甲', '西甲', '德甲', '中超', '五洲',];
            $cat_names = ['u23', 'Premier League', 'Serie A', 'La Liga', 'Bundesliga', 'Lục địa',];

        }
    } else {
        // $cat_names = ['英超', '意甲', '西甲', '德甲', 'NBA', 'CBA',];
            $cat_names = ['u23','Premier League', 'Serie A', 'La Liga', 'Bundesliga', 'NBA', 'CBA',];

    }
    $match_class = [];
    foreach ($cat_names as $key => $str) {
        $py_code = stripVN($str);
        $match_class[] = [
            'is_select' => $en_name == $py_code,
            'mat_name' => $str,
            'en_name' => $py_code,
        ];
    }
    if (!$en_name) {
        $match_class[0]['is_select'] = 1;
    }
    return $match_class;
}

function match_class($type, $en_name, &$aid): array
{
    if ($type == "top") {
        /* 17118=> "欧冠", 17178=>"欧联",17716=>"LPL", 17694=>"亚冠",*/
        $match_class = [];
        foreach (get_jf_class() as $key => $str) {
            $py_code = stripVN($str);
            if ($en_name && $en_name == $py_code) {
                $aid = $key;
            }
            $match_class[] = [
                'is_select' => $en_name == $py_code,
                'mat_name' => $str,
                'en_name' => $py_code,
            ];
        }
        if (!$aid) {
            $match_class[0]['is_select'] = 1;
            $aid = 1;
        }
        return $match_class;
    }

    $class_w = [];
    $key = "match_class_{$type}_{$en_name}";
    if ($type) {
        $class_w[] = ["match_cat_en_name", "=", $type];
    }
//  $group =[];// db("match")->field("id,mat_name, en_name, cat_en_name as match_cat_en_name")->where("mark_video", 1)->select();
    $group = db("ft_webcast")
        ->field("match_name as mat_name,match_en_name as en_name,match_cat_en_name,count(*) as ct")
        ->where("start_play", ">=", time() - 3600 * 3)
        ->where("start_play", "<=", time() + 86400)
        ->where($class_w)
        ->whereNotNull("match_en_name")
        ->group("match_en_name")
        ->limit(30)
        ->orderRaw(order_by_match())
        ->order("match_name")
//        ->cache($key, 60, "redis")
        ->select()
        ->toArray();
    $match_class = [];
    $match_class[] = ["mat_name" => lang('rec'), 'en_name' => "all", "is_select" => ($en_name == 'all' or $en_name == '')];
    foreach ($group as $item) {
        $match_class[] = [
            'is_select' => $en_name == $item['en_name'],
            'mat_name' => $item['mat_name'],
            'en_name' => $item['en_name'],
            'count' => $item['ct']??0
        ];
    }
    return $match_class;
}



function hot_match($field = "mat_name")
{
    return db("ft_match")->where("mark_video", 1)->cache("HOT_MATCH",86400)->column($field, "id");
}

function order_by_match($field = "match_name")
{
    $match_str = join("','", hot_match());
    return "field(`{$field}`,'{$match_str}') desc";
}

function ms($s=0){
    $sr = microtime(true)*1000;
    if ($s){
        return $sr-$s;
    }
    return $sr;
}

function get_hot_tags($cat_en_name=''){

    $tags = db("ft_match")
        ->withoutField(['title','keywords','description','intro','create_time'])
        ->where("mark_video", 1)
        ->where(function ($query) use($cat_en_name){
            if ($cat_en_name){
                $query->where('cat_en_name','=',$cat_en_name);
            }
        })
        ->cache(getTopHost()."_tags_live", 86400,"hot_tags_list")
        ->select();

    foreach ($tags as $k => $tag) {
        $tag['cat_en_name'] = $tag['match_cat_id'] == 1000 ? "zuqiu" : "lanqiu";
        $tag['match_cat_en_name'] = $tag['cat_en_name'];
        $tag['url']= DIRECTORY_SEPARATOR.trim(get_route("{$tag['cat_en_name']}_live"),DIRECTORY_SEPARATOR).DIRECTORY_SEPARATOR.$tag['en_name'].DIRECTORY_SEPARATOR;
        //"title"=>to_ft($tag['mat_name']),
        $tag['title'] = $tag['mat_name'];
        $tags[$k] = $tag;
    }
    return $tags;
}


function format_tag($data, $is_render = false, $class = 'el-tag el-tag--light')
{
    if (empty($data['tags'])) {
        return $is_render ? '' : [];
    }
    $tag_path = $data['is_video'] ? get_route('video_tag') : get_route('news_tag');
    $tags = is_string($data['tags']) ? json_decode($data['tags'], true) ?? [] : $data['tags'];
    $tag_str = '';
    $tmd = strtotime(date("Ymd"));
    foreach ($tags as $k => $tag) {
        $tags_code = $tag['code'];
        $tags_name = $tag['name'];
        $live_tag = db("ft_live_tags")->where("tag_code", $tags_code)->cache("{$tags_code}_$tmd", 86400)->find();
        if ($live_tag) {
            $url = DIRECTORY_SEPARATOR .trim( get_route("{$live_tag['cat_name']}_live"),"/") . DIRECTORY_SEPARATOR . $live_tag['tag_code'] . DIRECTORY_SEPARATOR;
        } else {
           $tag_path = trim($tag_path,"/") ;
            $url = "/{$tag_path}/{$tags_code}/";
        }
        if ($is_render) {
            $tag_str .= '<a class="' . $class . '" href="' . $url . '" target="_blank">' . $tags_name . '</a> ';
        } else {
            $tags[$k]['url'] = $url;
        }
    }
    return $is_render ? $tag_str : $tags;
}


function create_cache_key($key){
    return getTopHost()."_".(isMobile()?"mb":"pc")."_{$key}";
}


function get_text($body, $len = 80)
{
    $text = strip_tags($body);
    $text = str_replace(["\n", "  "], ["", " "], $text);
    return mb_substr($text, 0, $len);
}


function isSpider()
{
    try {
        $ua = strtolower(request()->header('user-agent'));
        return preg_match("/(googlebot|Baiduspider|360spider|bingbot|bytespider|yisouspider)/i", $ua);
        # $DNS = substr(gethostbyaddr($_SERVER["REMOTE_ADDR"]), -strlen("baidu.com")) === "baidu.com";
    } catch (\Exception $e) {
        return false;
    }
}



function is_open_live($is_set = "")
{
    $lk_path = runtime_path() . "/index.temp";
    if ($is_set === true) {
        @unlink($lk_path);
    }
    if ($is_set === false) {
        file_put_contents($lk_path, "");
    }
    return !((is_file($lk_path) and file_exists($lk_path)));
}



function in_str($str,array $need = []): bool
{
    foreach ($need as $value) {
        if (strpos($str, $value) !== false) {
            return true;
        }
    }
    return false;
}


function not_found($msg = "404")
{

    return view(root_path('view')."/404.html", ['msg' => $msg,"site_name"=>  conf_website("site_name")]);
}

function get_system_config($db=null){
    $find = db('ft_system_config',$db)->where('config_tab_id',1)->select();
    $conf = [];
    foreach ($find as $item){
        $conf[$item['menu_name']] = json_decode($item['value']);
    }
    return $conf;
}

function getFile()
{
    $fd = getTopHost();
    $dir = root_path("public/sitemap/$fd");
    $domain = conf_website("site_url");
    $fileArray=[];
    $time = time();
    if (file_exists($dir) && false != ($handle = opendir($dir))) {
        $i = 0;
        while (false !== ($file = readdir($handle))) {
            if ($file != "." && $file != ".." && strpos($file, ".")) {
                $href = "$domain/sitemap/$fd/$file";
                $fileTime = filemtime($dir . DIRECTORY_SEPARATOR . $file);
                $fileArray[$time-$fileTime] = ['file' => $file, 'href' => $href];
                $i++;
            }
        }
        //关闭句柄
        closedir($handle);
    }
    return $fileArray;
}

function init_database_config($top_host=null){
    $database_website = [];
    $db_list = [];
    if($top_host){
        $database_website=config("database_website");
        $db_find = db("ft_site")->where("top_host",$top_host)->find();
         if (!$db_find){
            exit("Site Not Found");
        }
        if ($db_find['assign_data']!=1){
            exit("Site Not Open");
        }
        $db_list[] = $db_find;
    }else{
        $db_list= db("ft_site")->select(); //->where("assign_data",1)
    }
    foreach ($db_list as $db){
        $database_website[getTopHost("https://".$db['top_host'])]=
            [
                'type'            =>'mysql',
                'hostname'        => env('DB_HOST', '127.0.0.1'),
                'database'        => $db['db_name'],
                'username'        => env('DB_USER', 'root'),
                'password'        => env('DB_PASS', ''),
                'charset'         => 'utf8mb4',
                'fields_strict'   => false,
                'fields_cache'    => true,
                'trigger_sql'     => false,
            ];
    }
    cache("database_website",$database_website);
    file_put_contents(root_path()."/config/database_website.php","<?php return ".var_export($database_website,true).";");
}

function init_system_config($db_name){
    $data = get_system_config($db_name);
//            $save_name = str_replace([".",'-'],"_",$site['top_host']);
    set_website_config($data,$db_name);
}



function create_admin_token($str): string
{
    return \app\service\Mcrypt::encode(json_encode(['id'=>$str,'time'=>time()],JSON_UNESCAPED_UNICODE));
}


function getAdminIdBytoken($str=null){
    
    if (!$str) {
        $str = request()->header("Authorization") ?: cookie("Authorization");
        if (!$str) {
            return false;
        }
    }
    $token_str = \app\service\Mcrypt::decode($str);
    if (!$token_str){
        return false;
    }
    $token = json_decode($token_str,true);
    if(empty($token)){
        return false;
    }
    if(time()-$token['time']>86400*7){
        return false;
    }
    return $token['id'];
}


function admin($key = null)
{
    $admin = \think\facade\Db::name("ft_system_admin")->where("id", getAdminIdByToken())->find();
    if ($admin) {
        if ($key) {
            return $admin[$key] ?? '';
        }
        return $admin;
    }
    return null;
}


function site_id($site_id = 0)
{
    $key = "SITE_ID_ADMIN_" . admin('id');
    if ($site_id) {
        cache($key, $site_id);
        return $site_id;
    }
    if ($site_id === null) {
        cache($key, null);
        return null;
    }
    return cache($key);
}


function parse_logo($logo)
{
    $purl = parse_url($logo);
    return "/" . ltrim($purl['path'], "/");
}


function get_http_code($url,$cookie_pre=''){
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url); //设置URL
    curl_setopt($curl, CURLOPT_HEADER, 1); //获取Header
    curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($curl, CURLOPT_TIMEOUT, 30);
    //curl_setopt($curl, CURLOPT_REFERER, "https://m.baidu.com/"); //设置Referer
    curl_setopt($curl, CURLOPT_HTTPHEADER, ['Token(wa1b2c3d4e5f6g7h8i9j0k_nihao)']); //获取Header
    curl_setopt($curl,CURLOPT_NOBODY,true); //Body就不要了吧，我们只是需要Head
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1); //数据存到成字符串吧，别给我直接输出到屏幕了
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); //数据存到成字符串吧，别给我直接输出到屏幕了
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false); //数据存到成字符串吧，别给我直接输出到屏幕了
//    curl_setopt($curl, CURLOPT_COOKIEJAR, runtime_path()."/{$cookie_pre}_cookie.txt");
    $content = curl_exec($curl); //开始执行啦～
    $code = curl_getinfo($curl,CURLINFO_HTTP_CODE); //我知道HTTPSTAT码哦～
    curl_close($curl); //用完记得关掉他
    return ["code"=>$code,'header'=>$content];
}



/**
 * @param $url
 * @param string $folder
 * @param string $replace_src
 * @return string
 */

function img_save_local($url, $folder = "thumb", $ext="jpg")
{

    try {

        $both = "!/both";
        if (strpos($url, $both) !== false) {
            $url = explode($both, $url)[0];
        }
        $both2 = "?";
        if (strpos($url, $both2) !== false) {
            $url = explode($both2, $url)[0];
        }

        $ext = "jpg";
        $f_name = md5($url) . ".$ext";

        $dt = date("Ymd");
        $local_path = "/static/img/$dt/";
        $save_path = public_path($local_path);
        file_exists($save_path) || mkdirs($save_path);
        $filename = $local_path . $f_name;
        $file_save_base = $save_path . $f_name;

        if (!file_exists($file_save_base)) {
            $url = str_replace(['wx2.sinaimg.cn', 'wx.sinaimg.cn', 'wx1.sinaimg.cn', 'wx3.sinaimg.cn', 'wx4.sinaimg.cn', 'wx5.sinaimg.cn', 'wx6.sinaimg.cn'], 'tva1.sinaimg.com', $url);
            $img_content = \libs\Http::get($url);
            if ($img_content) {
                @file_put_contents($file_save_base, $img_content);
                $img_content = null;
                $w = 300;
                $h = 400;
                if ($folder == 'cover') {
                    $w = 200;
                    $h = 100;
                }
                if ($folder=='content'){
                    # 获取 图片宽度
                    $im = new Imagick();
                    $im->readImage($file_save_base);
                    $yw = $im->getImageWidth();
                    $yh = $im->getImageHeight();
                    $w = 1000;
                    if ($yw>$w){
                        $h = intval($w*$yh/$yw);
                    }
                    $im->clear();
                    $im->destroy();
                }
                image_compression($file_save_base,$file_save_base,$ext,$w,$h);
            }
        }
        return "$filename";
    } catch (\Exception $exception) {
        show_err($exception);
    }
    return $url;
}

function show_err($exception)
{
    $msg = $exception->getMessage();
    $file = $exception->getFile();
    $line = $exception->getLine();
    $trace = $exception->getTraceAsString();
    $str = "【{$msg}】\n【{$file}】\n【{$line}】\n【{$trace}】";
//    send_console($str);
    dump($str);
}


function image_compression($file, $save = '', $ext = '', $width = 300, $height = 500)
{
    try {
        if (!file_exists($file) or !is_file($file)) {
            dump("文件不存在:" . $file);
            return;
        }
        //判断Imagick扩展是否开启
        if (!extension_loaded('imagick')) {
            dump("Imagick扩展未开启");
            return;
        }
        $im = new Imagick();
        $im->readImage($file);
        # $im->readImageBlob($file);
        $w = $im->getImageWidth();
        $h = $im->getImageHeight();

        if ($save == '') {
            if (strpos($file, ".") !== false) {
                $save = $file;
            } else {

            }
        } else {
            if (strpos($save, ".") === false) {
                $save = "$save/" . basename($file);
            }
        }

        if ($w > $width || $h > $height) {
//            if ($crop) {
//              #  $im->cropThumbnailImage($newWidth, $newHeight);
//            } else {
//            }
            $newWidth = $width;
            $newHeight = $height;

            if ($w > $newWidth) {
                $newHeight = intval($newWidth * $h / $w);
            }
            if ($newHeight > $height) {
                $newWidth = intval($height * $w / $h);
            }
            # $im->thumbnailImage($newWidth, $newHeight,true );
            $im->resizeImage($newWidth, $newHeight, \Imagick::FILTER_CATROM, 1, true);
        }

        if ($ext) {
            $im->setImageFormat($ext);
        } else {
            $ext = strtolower($im->getImageFormat());
            if ($ext == 'jpeg') {
                $ext = 'jpg';
            }
        }
        switch ($ext) {
            case "png":
                $im->setImageDepth(8);
                $im->setImageCompression(\Imagick::COMPRESSION_LZW);
                #dump($im->getImageCompressionQuality());exit;
                $im->setImageCompressionQuality(5);
                break;
            case "jpg":
                $im->setImageCompression(\Imagick::COMPRESSION_JPEG);
                $a = intval($im->getImageCompressionQuality() * 60);
                if ($a == 0) {
                    $a = 60;
                }
                $im->setImageCompressionQuality($a);
        }
        $im->stripImage();
        dump($save);
        $im->writeImage($save);
        $im->clear();
        $im->destroy();
        dump("压缩后: " . intval(filesize($save) / 1024) . " Kb");
    } catch (\Exception $exception) {
        show_err($exception);
    }
}



function send_console($text, $clientId = "all")
{
    try {
        $http = new \GuzzleHttp\Client();
        if (!is_array($text)) {
            $date = datetime(time());
            $content = json_encode(['type' => "log", 'data' => "【{$date}】：". $text]);
        } else {
            $content = json_encode($text, JSON_UNESCAPED_UNICODE);
        }
        if ($clientId == "all") {
            $clientId = "";
        }
        return $http->get("http://127.0.0.1:2121/?type=publish&to={$clientId}&content={$content}", [])->getBody()->getContents();
    } catch (GuzzleHttp\Exception\GuzzleException $exception) {
        return $exception->getMessage();
    }
}

/**
 * 导入数据库
 * @param $_sql
 * @param $database
 * @return bool|string
 */

function ImportQql($sql_file, $database)
{

    $msg = true;
    try {

        if (!is_file($sql_file) or !file_exists($sql_file)) {
            return "源数据库文件【{$sql_file}】丢失~";
        }
        $sql_text = file_get_contents($sql_file);
        $sql = explode("\n", $sql_text);
        if (is_string($database)) {
            $connect = database_temporary_connect($database);
        } else {
            $connect = $database;
        }
        $tempLine = '';
        foreach ($sql as $line) {
            if (str_starts_with($line, '--') || $line == '') {
                continue;
            }
            $tempLine .= $line;
            if (substr(trim($line), -1, 1) == ';') {
                $connect->execute($tempLine . ';');
                $tempLine = '';
            }
        }
    } catch (\Exception $exception) {
        $msg = $exception->getMessage();
    }
    return $msg;
}



function get_template_list()
{
    $list = [];
    $dir = root_path("view/tpls");
    $files = scandir($dir);
    foreach ($files as $file) {
        if (in_array($file, ['.', '..'])) {
            continue;
        }
        if (is_dir($dir . "/" . $file)) {
            $list[] = $file;
        }
    }
    return $list;
}

//临时数据库连接
function database_temporary_connect($database)
{
    $conf = config("database");
    $conf_item = \config("database.connections.mysql");
    $conf_item['database'] = $database;
    $conf_item['fields_cache'] = false;
    $conf['connections'][$database] = $conf_item;
    \think\facade\Config::set($conf, 'database');
    return \think\facade\Db::connect($database, false);
}

//获取导入xls数据
function excel_import($xlsFilePath)
{
      # 如果是 XLSX 格式，可以使用 Excel5
    if (pathinfo($xlsFilePath, PATHINFO_EXTENSION) == 'xlsx') {
        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
    } else {
        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xls();
    }
// 读取 XLS 文件
    $spreadsheet = $reader->load($xlsFilePath);

// 获取第一个工作表
    $sheet = $spreadsheet->getActiveSheet();

// 获取行数和列数
    $rows = $sheet->getHighestRow();
    $cols = $sheet->getHighestColumn();

// 读取数据
    $data = [];
    for ($row = 1; $row <= $rows; $row++) {
        $rowData = [];
        for ($col = 'A'; $col <= $cols; $col++) {
            $val = $sheet->getCell($col . $row)->getValue();
            if (gettype($val)=='object'){
                $val = $val->__toString();
            }
            $rowData[] = $val;
        }
        $data[] = $rowData;
    }
    return $data;
}


function auto_add_field($field, $table = 'ft_match', $type = 'varchar(50)')
{
    #'cat_en_name'
    if (!count(\think\facade\Db::query("show columns from $table like '$field'"))) {
        \think\facade\Db::query("alter table {$table} add `$field` {$type} null;");
    }
}

function check_db($field = "",$site_id=0)
{
    $site_id = $site_id ?: site_id();
    $site = db("ft_site")->where("id", $site_id)->find();
    if (!$site) {
        $site = db("ft_site")->where("db_name", env('DB_NAME'))->find();
    }
    if ($field) {
        return $site[$field] ?? '';
    }
    return $site;
}


function get_site_dbs($fun, $is_all = false)
{
    $w = [];
    if (!$is_all) {
        $w = [['assign_data', '=', 1]];
    }
    $dbs = db_main("ft_site")->where($w)->select();
    foreach ($dbs as $db) {
        $fun($db);
    }
}


function score_to_vi(&$data_list)
{
    $path = root_path('/app/config');
    $sc_json = $path . "/score.json";
    if (!file_exists($sc_json)) {
        return false;
    }
    $hd_arr = include("{$path}/header.php");
    $dict = json_decode(file_get_contents($sc_json), true);
    $new_title = $hd_arr["NEW_TITLE"];
    foreach ($data_list['rounds'] as $k => $round) {

        foreach ($round['content']['header'] ?? [] as $hk => $hd) {
            $th_txt = $hd_arr[$hd] ?? "";
            if ($th_txt) {
                $round['content']['header'][$hk] = $th_txt;
            }
        }

        $nm1 = $round['content']['name'];
        if (in_array($nm1,array_keys($new_title))){
            $round['content']['name'] = $new_title[$nm1];
        }

        $ldd = $round['content']['data'];

        if (isset($ldd[0])) {

            foreach ($ldd as $k2 => $item) {
                if ($round['template'] == 'team_point_ranking_group') {
                    $nm2 = $item['name'];
                    if (in_array($nm2,array_keys($new_title))){
                        $round['content']['data'][$k2]['name'] = $new_title[$nm2];
                    }
                    foreach ($item['data'] as $k3 => $item2) {
                        $team_name2 = @$item2['team_name'];
                        $vi_txt2 = $dict[$team_name2] ?? '';
                        if ($vi_txt2) {
                            $round['content']['data'][$k2]['data'][$k3]['team_name'] = $vi_txt2;
                        }
                    }

                } elseif ($round['template'] == 'team_point_ranking_regular') {
                    $team_name = @$item['team_name'];
                    $vi_txt = $dict[$team_name] ?? '';
                    if ($vi_txt) {
                        $round['content']['data'][$k2]['team_name'] = $vi_txt;
                    }
                } elseif ($round['template'] == 'team_point_ranking_match') {
                    $Ateam_name = @$item['team_A_name'];
                    $Bteam_name = @$item['team_B_name'];
                    $viA_txt = $dict[$Ateam_name] ?? '';
                    $viB_txt = $dict[$Bteam_name] ?? '';
                    $viA_txt&&$round['content']['data'][$k2]['team_A_name'] = $viA_txt;
                    $viB_txt&&$round['content']['data'][$k2]['team_B_name'] = $viB_txt;

                }
            }
        }
        $data_list["rounds"][$k] = $round;
    }
}





/**
 * @param $data
 */
function add_article($data,$db_name)
{

    try {
        if (!$data['body']) {
            return false;
        }
        $table = 'ft_article';
        $title = $data['title'];
        $union_str = @$data['id'];
        $match_data = @$data['secondary_category'];
        $thumb = $data['local_thumb'];
        $data['cat_en_name'] = db("ft_match_cat")->cache("CACHE_{$data['category']}", 3600)->where("cat_name", $data['category'])->value("cat_en_name");
        $content = $data['body'];
        //去掉content中的a标签
//        $content = strip_tags($content,"<div>|<img>|<span>|<i>|<b>");
        if ($data['is_video']){
            $check_res = check_tags_add($data['topic_tags']);
            $tags_arr =  $check_res['tags'];
            $tags = json_encode($tags_arr,JSON_UNESCAPED_UNICODE);
        }else{
            $tags = $data['topic_tags'];
        }
        
        $insert_data = [
            'title' => $title,
            'thumb' => $thumb,
            'content' => trim($content),
            'match_id' => $match_data,
            'tags' => $tags,
            'create_time' =>  time() ,// $data['create_time'],
            'update_time' => time(),
            'cat_name' => $data['category'],
            'other_data' => $data['topic_tags'],
            'cat_en_name' => $data['cat_en_name'],
            'video_info' => $data['video_info'],
            'is_video' => $data['is_video'],
            'is_rewrite' => 1,
            'is_show' => 1,
            'union_str' => $union_str,
        ];

        if (db($table,$db_name)->where("union_str", $union_str)->count() < 1) {
            return !!db($table,$db_name)->insertGetId($insert_data);
        }
        return false;
    } catch (\Exception $exception) {
        return  ["code"=>1, "msg"=>"\n[LINE:{$exception->getLine()}] FILE:{$exception->getFile()}\nMSG：". $exception->getMessage(),];
    }
}


function check_tags_add($tags_arr = [])
{
    $tag_ids = [];
    if (!$tags_arr){
        return $tag_ids;
    }
    if (is_string($tags_arr)){
        $tags_arr= json_decode($tags_arr,true);
    }
    $new_tags=[];
    foreach ($tags_arr as $tag) {
        if (empty($tag) or in_str($tag['name'],['topic'])) {
            continue;
        }
        $tag_id = db("ft_tags")->where('title', $tag['name'])->value("id");
        if (!$tag_id) {
            $tag_id = db("ft_tags")->insertGetId(['title' => $tag['name'], 'tag' => $tag['code'], 'create_time' => time()]);
        }
        $tag_ids[] = $tag_id;
        $new_tags[] = $tag;
    }
    return ["ids"=>$tag_ids,"tags"=>$new_tags];
}



function insert_data_full(&$data)
{
    try {
        //match_cat_en_name
        $cat_arr = ['zuqiu' => 1000, 'lanqiu' => 1001];
        $match_name = $data['match_name'];
        $match_cat_id = $data['match_cat_en_name'];
        if (!$data['match_en_name']) {
            $en_name = db("ft_match")->where("mat_name", $match_name)->value('en_name');
            $count = db("ft_webcast")->where("match_name", $match_name)->where("start_play", ">", strtotime(date("Y-m-d")))->count();
            if ($en_name) {
                $data['match_en_name'] = $en_name;
                $upd = [
                    "sum_live_num" => $count,
                ];
                if ($match_cat_id) {
                    $upd['match_cat_id'] = $match_cat_id;
                }
                db("ft_match")->where("mat_name", $match_name)->update($upd);
            } else {
                $find = db("ft_match")->where("match_cat_id", $match_cat_id)->find();
                $en_name = str_replace(["-"," "],"_",$match_name);
                $data['match_en_name'] = $en_name;
                db("ft_match")->insert([
                    "mat_name" => $match_name,
                    "en_name" => $en_name,
                    "sum_live_num" => $count,
                    "title" => $find['title'],
                    "keywords" => $find['keywords'],
                    "description" => $find['description'],
                    "create_time" => time(),
                    "match_cat_id" => $match_cat_id,
                    'cat_en_name'=>$match_cat_id
                ]);
            }

        }
    } catch (\Exception $exception) {
        dump('ERR form insert_data_full Line : ' . $exception->getLine() . " error:" . $exception->getMessage());
    }
}

function add_to_live_tag($tagName, $tagCode, $catName, $playTime = 0)
{
    $find = db('ft_live_tags')
        ->where('tag_name', $tagName)
        ->where('cat_name', $catName);
    $tag = $find->find();
    if ($tag) {
        if ($tag['play_time'] < time()) {
            $find->update([
                'play_time' => $playTime
            ]);
        }
    } else {
        db('ft_live_tags')->insert([
            'tag_name' => $tagName,
            'tag_code' => $tagCode,
            'cat_name' => $catName,
            'play_time' => $playTime,
        ]);
    }
}


function cat_tags($str, $db_name = '')
{
    $tags = [];
    if ($str) {
        $str_arr = is_array($str) ? $str : explode(",", $str);
        $py = new \app\service\Pinyin();
        foreach ($str_arr as $value) {
            if (in_str($value,['pic']))
            {
                continue;
            }
            $mc = db("ft_tags")->where("title", $value)->find();
            $count = db("ft_article", $db_name)->whereLike("tags", "%\"{$value}\"%")->count();
            if ($mc) {
                $vi_code = $mc['vi_code'];
                $vi_name = $mc['vi_name'];
                db("ft_tags")->where("id", $mc['id'])->update(['atc_count' => $count]);
            } else {
                $trans = getTranslateText($value);
                $vi_name = $trans['content']??"";
                $vi_code = stripVN($vi_name, '-');
                db("ft_tags")->insert([
                    "title" => $value,
                    "tag" => $py->TransformWithoutTone($value),
                    "atc_count" => $count,
                    "vi_code" => $vi_code,
                    "vi_name" => $vi_name,
                    "create_time" => time(),
                ]);
            }
            $tags[] = ["name" => $vi_name, "code" => $vi_code];
        }
    }
    if ($tags){
        dump("TAG format Success");
    }
    return json_encode($tags, JSON_UNESCAPED_UNICODE);
}



function lastId($list){
    if ($list->last())
    {
        return $list->last()['id'];
    }
    return  0;
}

function stripVN($str,$sp='-') {
    $str = preg_replace("/(à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ|ä)/", 'a', $str);
    $str = preg_replace("/(č|ć)/", 'e', $str);
    $str = preg_replace("/(è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ)/", 'e', $str);
    $str = preg_replace("/(ì|í|ị|ỉ|ĩ)/", 'i', $str);
    $str = preg_replace("/(ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ|ö)/", 'o', $str);
    $str = preg_replace("/(ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ|ü)/", 'u', $str);
    $str = preg_replace("/(ỳ|ý|ỵ|ỷ|ỹ)/", 'y', $str);
    $str = preg_replace("/(đ)/", 'd', $str);
    $str = preg_replace("/(ñ)/", 'n', $str);
    $str = preg_replace("/(ž)/", 'n', $str);

    $str = preg_replace("/(À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ)/", 'A', $str);
    $str = preg_replace("/(È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ)/", 'E', $str);
    $str = preg_replace("/(Ì|Í|Ị|Ỉ|Ĩ)/", 'I', $str);
    $str = preg_replace("/(Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ|Ö)/", 'O', $str);
    $str = preg_replace("/(Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ)/", 'U', $str);
    $str = preg_replace("/(Ỳ|Ý|Ỵ|Ỷ|Ỹ)/", 'Y', $str);
    $str = preg_replace("/(Đ)/", 'D', $str);
    $str = preg_replace("/(Š)/", 'S', $str);
    $str = preg_replace("/(Ñ)/", 'N', $str);
    $str = preg_replace("/('|~)/", $sp, $str);
    $str = preg_replace("/(ø|\(|\)|`|’)/", "", $str);
    if ($sp){
        $str = preg_replace('/\s+/', $sp, $str);
    }
    return $str;
}



function getTranslateText($text,$to='vi')
{
    $header = [
        CURLOPT_USERAGENT => 'AndroidTranslate/5.3.0.RC02.130475354-53000263 8.1 phone TRANSLATE_OPM5_TEST_1'
    ];
    $option = [
        CURLOPT_PROXY => 'http://127.0.0.1',
        CURLOPT_PROXYPORT => '10808',
        CURLOPT_PROXYTYPE => CURLPROXY_SOCKS5,
    ];
    if (PHP_OS == 'WINNT') {
        $header = $header+ $option;
    }
    $api = "https://translate.google.com/translate_a/single?client=gtx&dt=t&dj=1&ie=UTF-8&sl=zh-CN&tl={$to}";
    $title_rs = \libs\Http::post($api, ['q' => $text], $header);
    return [
        'status' =>true,
        'content' => getSentencesFromJSON($title_rs),
    ];
}

function getSentencesFromJSON($json)
{
    $sentencesArray = json_decode($json, true);
    $sentences = "";
    $sentences_arr = $sentencesArray["sentences"]??[];
    foreach ($sentences_arr as $s) {
        dump("ORG:{$s['orig']} - TRANS:{$s["trans"]}");
        $sentences .= $s["trans"] ?? '';
    }
    return $sentences;
}



function removeEmojiAndSpecialChars($string) {
    // 过滤掉Emoji表情
    $regexEmoticons = '/[\x{1F600}-\x{1F64F}]/u';
    $cleanString = preg_replace($regexEmoticons, '', $string);
 
    // // 过滤掉其他符号和图片等
    // $regexSymbols = '/[\x{1F300}-\x{1F5FF}]/u';
    // $cleanString = preg_replace($regexSymbols, '', $cleanString);
 
    // // 过滤掉运输和地图符号
    // $regexTransport = '/[\x{1F680}-\x{1F6FF}]/u';
    // $cleanString = preg_replace($regexTransport, '', $cleanString);
 
    // // 过滤掉部分Unicode符号
    // $regexMisc = '/[\x{2600}-\x{26FF}]/u';
    // $cleanString = preg_replace($regexMisc, '', $cleanString);
 
    // // 过滤掉其他特殊符号
    // $regexOthers = '/[\x{2700}-\x{27BF}]/u';
    // $cleanString = preg_replace($regexOthers, '', $cleanString);
 
    // // 过滤掉其他所有非字母非数字符号
    // $cleanString = preg_replace('/[^A-Za-z0-9]/', '', $cleanString);
 
    return $cleanString;
} 

function aes_encrypt($data, $key)
{
    $str = openssl_encrypt($data, 'AES-128-ECB', $key, OPENSSL_RAW_DATA);
    return base64_encode($str);
}

// AES-256-ECB解密函数
function aes_decrypt($encryptedData, $key)
{
    $encryptedData = base64_decode($encryptedData);
    return openssl_decrypt($encryptedData, 'AES-128-ECB', $key, OPENSSL_RAW_DATA);
}

function mkdirs($dir, $mode = 0777): bool
{
    try {
        if (!is_dir($dir)) {
            mkdirs(dirname($dir), $mode);
            mkdir($dir, $mode);
        }
        return true;
    } catch (\Exception $e) {
        return false;
    }
}


function mysqli($database): mysqli
{
    return new \mysqli(env('DB_HOST'), env('DB_USER'), env('DB_PASS'), $database);
}

function get_db_query($db_name, $sql)
{
    $sql_i = mysqli($db_name);
    $rest = $sql_i->query($sql);
    if ($rest and gettype($rest) != 'boolean') {
        $rs = mysqli_fetch_assoc($rest);
        mysqli_free_result($rest);
        $sql_i->close();
        return $rs;
    }
    return $rest;
}

function to_code($text)
{
    $text = trim($text);
    $text = str_replace(' ', '-', $text);
    return $text;
}




function removeXSS($val)
{
    $val = preg_replace('/([\x00-\x08,\x0b-\x0c,\x0e-\x19])/', '', $val);
    // this prevents like <IMG SRC=@avascript:alert('XSS')>
    $search = 'abcdefghijklmnopqrstuvwxyz';
    $search .= 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $search .= '1234567890!@#$%^&*()';
    $search .= '~`";:?+/={}[]-_|\'\\';
    for ($i = 0; $i < strlen($search); $i++) {
        $val = preg_replace('/(&#[xX]0{0,8}' . dechex(ord($search[$i])) . ';?)/i', $search[$i], $val); // with a ;
        $val = preg_replace('/(&#0{0,8}' . ord($search[$i]) . ';?)/', $search[$i], $val); // with a ;
    }
    // now the only remaining whitespace attacks are \t, \n, and \r
    $ra1 = array('javascript', 'vbscript', 'expression', 'applet', '<meta', '<xml', 'blink', '<link', '<style', '<script', '<embed', 'object', '<iframe', '<frame', '<frameset', '<bgsound');
    $ra2 = array('onabort', 'location', 'window.', 'onactivate', 'onafterprint', 'onafterupdate', 'onbeforeactivate', 'onbeforecopy', 'onbeforecut', 'onbeforedeactivate', 'onbeforeeditfocus', 'onbeforepaste', 'onbeforeprint', 'onbeforeunload', 'onbeforeupdate', 'onblur', 'onbounce', 'oncellchange', 'onchange', 'onclick', 'oncontextmenu', 'oncontrolselect', 'oncopy', 'oncut', 'ondataavailable', 'ondatasetchanged', 'ondatasetcomplete', 'ondblclick', 'ondeactivate', 'ondrag', 'ondragend', 'ondragenter', 'ondragleave', 'ondragover', 'ondragstart', 'ondrop', 'onerror', 'onerrorupdate', 'onfilterchange', 'onfinish', 'onfocus', 'onfocusin', 'onfocusout', 'onhelp', 'onkeydown', 'onkeypress', 'onkeyup', 'onlayoutcomplete', 'onload', 'onlosecapture', 'onmousedown', 'onmouseenter', 'onmouseleave', 'onmousemove', 'onmouseout', 'onmouseover', 'onmouseup', 'onmousewheel', 'onmove', 'onmoveend', 'onmovestart', 'onpaste', 'onpropertychange', 'onreadystatechange', 'onreset', 'onresize', 'onresizeend', 'onresizestart', 'onrowenter', 'onrowexit', 'onrowsdelete', 'onrowsinserted', 'onscroll', 'onselect', 'onselectionchange', 'onselectstart', 'onstart', 'onstop', 'onsubmit', 'onunload');
    $ra = array_merge($ra1, $ra2);
    $found = true; // keep replacing as long as the previous round replaced something
    while ($found == true) {
        $val_before = $val;
        for ($i = 0; $i < sizeof($ra); $i++) {
            $pattern = '/';
            for ($j = 0; $j < strlen($ra[$i]); $j++) {
                if ($j > 0) {
                    $pattern .= '(';
                    $pattern .= '(&#[xX]0{0,8}([9ab]);)';
                    $pattern .= '|';
                    $pattern .= '|(&#0{0,8}([9|10|13]);)';
                    $pattern .= ')*';
                }
                $pattern .= $ra[$i][$j];
            }
            $pattern .= '/i';
            $replacement = substr($ra[$i], 0, 2) . '<x>' . substr($ra[$i], 2); // add in <> to nerf the tag
            $val = preg_replace($pattern, $replacement, $val); // filter out the hex tags
            if ($val_before == $val) {
                // no replacements were made, so exit the loop
                $found = false;
            }
        }
    }
    return $val;
}




/**
 * 将时间戳转换为友好时间差格式
 * @param string|int $datetime 时间戳或日期字符串
 * @return string 格式化后的时间差（如：5秒前、3天前）
 */
function formatTimeAgo($datetime) {
    // 转换为时间戳
    $timestamp = is_numeric($datetime) ? (int)$datetime : strtotime($datetime);

    // 计算时间差（当前时间 - 目标时间）
    $diff = time() - $timestamp;

    // 处理未来时间和无效时间
    if ($diff < 10) return 'Baru saja';
    if ($timestamp <= 0) return '--';

    // 按时间差区间返回不同格式
    if ($diff < 60) {
        return $diff . ' detik yang lalu';
    } elseif ($diff < 3600) {
        $minutes = floor($diff / 60);
        return $minutes . ' menit yang lalu';
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return $hours . 'jam yang lalu';
    } elseif ($diff < 2592000) { // 30天（30*24*3600）
        $days = floor($diff / 86400);
        return $days . ' hari yang lalu';
    } else {
        return date('Y-m-d', $timestamp);
    }
}


function pathEq($path1,$path2)
{
    $path1 = $path1?trim($path1,'/'):'';
    $path2 = $path2?trim($path2,'/'):'';
    if ($path1 == $path2){
        return true;
    }
    return false;
}

function getImgUrl($src,$domain='http://www.dailylive.net')
{
    if (str_starts_with($src,'http')){
        return $src;
    }
    if (str_starts_with($src,'/')){
        return $domain.$src;
    }
    return $domain.'/'.$src;
}
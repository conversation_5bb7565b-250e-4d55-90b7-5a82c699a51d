<?php
declare (strict_types=1);

namespace app\command;

use app\model\Live;
use DiDom\Document;
use libs\Baota;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\Db;
use think\facade\Config;

use think\Http;

class Test extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('test')
            ->setDescription('the test command');
    }
    private $key = "KVksL2jJ6eLOP7cX";

    protected function execute(Input $input, Output $output)
    {

    $url = "https://bf.jllivese.com/live/322582_hd01.m3u8?txSecret=e884575c584d27d33f77a676815c4ad5&txTime=67e984f6";
    $parse_url = parse_url($url);
//        dd(datetime(1744563600));
//        $content = <<<EOT
//wEASCGA0lLdNZB0qLiK6KRPg47BxZmnAllUFW2XZWiBYGsZtXRYjCkRfv7vCUEn2UCvL/GzetRyj9RtJnb2LCr6Hp32CPwxirWTsJkqqlHyBKQAiJxedK/9g3dyif/hZpG/HtYvqcWd01aZySvkcjiuZp6vdYIIbz22GRQfDYHcujOSACgsqEUoicHsE2qGHOG7IUqmkJZUW8HOGWcvM3niXg7XX8xzJai56GB+kk37BBhazyeE/RmZMRJrhGQGj2KAPL8mz08J6yEbtug9BiqottWX5fC9nzyzREnEMooWx9g8YyIvK/moOUhrprv7yZkmZUMbfKjHeRuZK924QddRBd+N599YSLAMTyWwsedTv4GS0u9Bm0DdpaagrDnTPgX+bWGb0S9DmOOWBaM/Dfxbqn3RzBdWpl9rUM9oDPSBQBGW44GN59z9Tu8GgGLcJN5PNIuJxd8O/wbQJuk7982WoEU36b7MMoZConDVXgDiMqdilOddiJQNnABPGNeBlqYkhNfmuU5MnhKP5RoSODmmv0dV3XzYAHWDvU2O/XgPbOMw6TTpnAuKCWmFjaEo7f77VGpbdk4ldWhx2nyDwTPpeFqH261CcfJaijHtHfN1J4Nj1YlsKq9qjwkVPCRp7gHnWItT1g0BIurZ+GFRvyOdsPb5EEKS3MXHqdjTGsWJY0Yf/UovWxofgfHb4tT4rA1MmXtaEwxup+Z41ADn8UUBbdz+NYuPeYo4yHssiMgzoKu33JX0mzrzIpxFOvSlz+l4WofbrUJx8lqKMe0d83Ung2PViWwqr2qPCRU8JGntRhBCuXJmXPUym+L33HTqQi4xeexUqtE2TNXBaukXw5PqTK8KiJS1LSjqvEDP92dz5sgCW3jOFObQmfy7A/n8r1nizg1FiQTdRrceiJQA3NXiaYrb/mhI6ksBAjPNI1+M24kLr/o78IwKBGH+OQuhvRZXXEZmj84DlPNSqaiyxxA+8tqCwkd3C3Z35jhbZo6FQzh+i+WUCGU5dQx/VoukY+bIAlt4zhTm0Jn8uwP5/K+dmXR041pZafpdeCTlCzBt4mmK2/5oSOpLAQIzzSNfjNuJC6/6O/CMCgRh/jkLob6Dc3IF/vNf7uDm/mH2NLkDMRYMF6wGCJNhUv4BTJDUN9qOyPr3ERN4VFXoysxmd1pwW4zgSEB9htFZrgYG4bfvFAiC7kIcziCp2WmkUsq8IwQYWs8nhP0ZmTESa4RkBo9igDy/Js9PCeshG7boPQYqGDgUbEa+lYmMrhVdN57uHM6at//xqzOEZ8LwyLLGAhqlcHQ+lUPIOls9rTj/HT6TDJzQcfbq1eJn28vatKjpYC7zs9mm1WG3yUavEWLMGaW+P7AuHVq9r7eXbJCwgdVHnkuG1wPpDgZmtp155aXQzuuuHH4Vcijz22wSkY/1JuAOISPHe8uldSTNr7paFwMhuc00mlekpLyIVH22oDJTAwTfY3QgOd6G9x+IOc4Cny/iLsnFtnuX4Y1blFRe+GL3D1DWHL8EbatxdLll4z41sWVwH5BUNVvRNiAzmYaI4ftjsVTQ/klmd2iX3PjKG5ns/ihFKzoCljd/L93jv4y555QNlKXPJxs/JaLilEy909UQMZuSGvzFOz0K0rxRvlDhwsNwR5c9ybvOB4r9ZHr7Xr9jZdRpLVIO+JCAiqJHh0MYMWAn7JHsrmOSh/E9++6pZXAfkFQ1W9E2IDOZhojh+2OxVND+SWZ3aJfc+Mobme0HJf3IwTYZssPByXRPESLuw/L58+U5g40cxgEmUftbKCyPf2faNcrSZxe5LOMD7MFNYdfa5Cek+JDIaNUFHmXR81CMXK1cuI3idYhd4UqYlkpUaxuV3hvEbx1b4ZC7tzz0kZ7wk0ouecyrluptmBO5mYYpQz9WlX4o7qmI1qHHhuDh3yHm3aUi9tUwr+fmYraY6GcrkiQGfg3B1qkvZVRxARnS6X0+a0q6G6G7nscvTXI0C9RJzRZAAObRYH4JkgcExuz3bhkpRt6h6+feRQ/LX3R+x64C6db7nm7+4nckUH6bXhb3M/adg4jutH8QGO8ceTNibhJCwBLGRNCEPB9qXN1SlSjq78l/UOZnVM+Du6bdghiQbpgNPluYEliFjamoS3V5CNgFiqwivcfRkp3S/qPORpEB9L1/fVnE/AWtYczUvLUNJfcG+59//VPAYjVgbJMkfrg8bE6ySKq88puLsfGi8SNMQvhKlbgCu8r8CJ4sywAI5Z65XMDK36lMAJ3jaXJEQDMJscsKNYG3P3Oje1fKyFSpFFiyZvE8PbmOImqP+Bmt+riHTkfR7l4ZX6EIewh5UmhEwfYo4QGjwtuYKKvc8bf+utfPEkjlLeMrneEkOkyoa6NevFl3fKeNrl3ujbEimJoUdAnr8jdezv0OgYTNkhMBiiGUbn3qwsUA526O2WvvtmbaW5vrVllUSa5qj/gZrfq4h05H0e5eGV+gPxxw9e8N94+PNJJtz48RN9BpRYy2tOE39F9ra4Mx5DgDUuxGlo4UR8U2KPG96Vbd7o2xIpiaFHQJ6/I3Xs79DsH2DmNYnlatrjbL2DswRXG37dSJZgvKNygfLg9i+krALm80nhNW8bwHm0983tQtW/lLSiNfr3FhFR2Ne8fLgOcGe7Te8JPNzHmH+CUC0Kf1iJcK58SxTzExvT5i8zJI1+YL/howPtCIo6ZIMYILFsIF/m1hm9EvQ5jjlgWjPw38W6p90cwXVqZfa1DPaAz0g9MQwpsXc0/MddDX6BOY563XE8HVskhfx1ERk/qs8oxTalBBb1SsYWOtiyBZyuzPyY+qRwhmyzNE/gYHs2yLvFFZCXATwECHJJL7Nk3JVhnmT7uE/Z9lXKvBRLqROOlcjJ4sywAI5Z65XMDK36lMAJ41Jhyp/UcQXouMq8t3eDy6sGCl8pXEiwMwuZljEmVEQclvu3gi5bnmB9KHY2LXXbTQ6qF2kUjXBKFISzfBTsRCM7UBHQjaAWhR5JTSesTrQiUovou7x6GT4ghgMo4gI7ynIHfMLTmoj/LAHVZR6bNRUprei36ZtaZAXbUOM8xWtUcr1qwaMKK6wkCqX6vJiS2F3kzx0J1uKEB0slrb/J5Mvlu9Zi1kx8TcP8Y8hG3WdAD4FuOJW20tw9ObXL4HdYPOTuO36WvyZhIub+v1uPrQoobfuZJDW7+TGqbfPEH23d/XaEgRfyKkn9YncItJTF1w0bpkmcuyJMPxPxPjs8K2akJaX6sVlXIyHe8y5+VODuLUEs/iUYwLxC8zb3bw6soBE30gYxBAGwPQjaZArEQOrb18NFJ19TTtH/37wmnV8Bx1AqmX//Yk5l8aD0ltkGA+ZBGAsk+DUc82MYQGr+6lCPQs2Qr+RKSCxbrB/7qxk5zk7AjatpSI2mlU3ZeSU8XGr7i6koitm6lc0r2Zq/MrVM0KsTUIH0rDzNn8lD6PLb3VJLX7p6Dsu8uITesrqeyqeKdoR0S377SxGXcjd0xjo1tjEBq/jB7mcUZaogeMy/966cUmJcbTD5VoYJKod1KyTJFWmFla0Xi8hm30rnOb3Fv5kN0ONkzohc7rOfwWzIIKTpYLlT8vegewxorG+98gSkEfHHDhYEKIQiO5coOJjRGtYLBZFSWO46TWkuNfFag29EIzwsnU42JHWSC4cvfk2KGZGOWZCv306OwTDMbNFjX/50AELxWsgWMyuUMZCckpE9cmYvtILWe1sHswbULnGftlxPdjqn+ncOFIv7UjSxTHtO5XEeXNHmGSv0kC2Zz8Aw8lNyFQbaVISw5deDij90qrzkElIRI10caN/NU89kbmc+9jxWbtFxzynb4Wuw/L8CFLwp2QC5rilXNpaGLiQiifXSYKEIpAZA5bSaUHevUnhEGRDYNgaQjq5qfqcztYBnrFSXpLRUlQdh2OZEIoax1W2B5OdQ+VlCFU9fzAfBPkYRbN7b4kHb/XVNm9PFez+w8KmmuCeSQZrrEc1fX12NiApmvfPCCvm/+VyKDlPFO4fX49xvwhOM/TQpRQe7N03X8qKuAeePLkboTL8Hqg8mXqPcS2wSww6vtGHN2x+CoCUVQ8jfnmAVAFggEjKtxsrZIoC6bVQhWCQ053tvPheOOqH3UHNdmEqd7hyl3I3oGAMuZSXnx4GpkKYl8Rpc74qobYvVz3wZb2cN5ZlqgexRDPoutUsuJ5JcxCY2CGWMPvmnPNiFel6WvNNrKyyrQdaSs34HHBYZpZVEeXvysQcCX3y9LslJHEwSj4E+Gge/++8OP6MbBy3MUnRX+n77zZFfm+sgLTPs/nzp10gxjkNHH2GeagyLktMFcaFbC4GKo9YafJM5wSNNJ2garTlY9V0kEvYLn8ReC67ppkJJfqD/zRAnHRDBQf84zn7sKWrfcR+Z1ryJSJG9c4aTKAq0CWPTZSlZ35oeor7AFSkpvV8KKHcIjT783gYlmZagdWSlRrG5XeG8RvHVvhkLu3Ph3jXN7CfxeLVEJzUvOgzCmqhq4tCOKWrigNcjP39Y9ItWEpb1sOYGRPKGUb/ikj/mGSN78JlxH8pGg2YBNO4vGd6TvvDeC4VbyXEgtMG8gwuQZq+a+VJFvXE6CUrybbIumU56igxtIZIG/CE0id94jjKFEYhUEap58XQI5blL3wGmhWKjx7eQTS0uPlbR0Kp9aTyX9r8RdiFkdUfpRbx4qD0DOKsw70YP5fV0lfBOvwG8N9wDjY1LHflIqAiyCh4kmlodVte/HWO/4dtSjzhNq0rD6BgTQCbdqTuV3wCDUCriQ0Pb21LofbpCi98eqP/4dvSLsiO8h8WKslkNrYGXACdrHQoCZNGyYn8Fi+ST7hMwYdiqePF+ifiAMcJ5uQfe+8FXaE3AvKIsXqr7Eu3zhkSD2BPTX5qrwDpDvENB1hpU8tGJh4CvPpOF9++fagnJUpGpm/J0FTQjbMDTH60bT5opkGxs2zKYpWfvT5Uy1oZUXk65pqg0vDUwQ+t2iF0g4U8JgXdclek1Br8DlGhO1RpHqMG+GeaXkUHr2siDuntWBCt53YB6WfNPsVCmutCmRGwiZyr8Au1uOeVBcwDb7Csf+kEnGzo+5mFoW+LgsmlNQ9+zcXzSnMNHCDldSk9cclybgWASPvn9zGxY7R3Tx0jauGb/DOYh4BbIMVzqQHjKiNbFpw1J5gK7iMgLfiabP0WSBjYcergN6KeymIij6f1W3TjlYgNZlPa6gdI+WEi2iQ62yxOhkOuUCHIJ/+03DKnP7W/s+GLdYBWNuKy8P1flLTyAAZFCso9vRVQrMo/v/zvY2cY/1fVZTH66CEnpRHYoQREV+YX71C3JaF+yIMYISGTRJGV+dGsWV3PhRPZtbQWJZkqfp2j5RACM9z4nlDA9H4NtOjOGRzjxzifrxY4wKBVtXjt6JpfLWomvn5FmcTBgCM0QsUiZ3jJ1sOiVaCJYOsIkujSrgNQSBabimzgzhkb5MtgrmdWd/sufeU0gmTzohMsdGA281zFa3Q5wRK1EpY7K1vKrjN0FQHs8tkfYLvEDDy54Ua6ClsysULjhdgkok9EDj+iCvpO1rATTFQZQcHog7G4/nwkRGorNnI+j+HWSNmEm3jxh6fEb751KFgo8OCUb6FYxDWLZMLJXo18q7RAofV6XjPVaBcvj/HJ/g0C+wTbUmFtHW+hbpGm3Ri/TwKPmavytgeC4T+bHzkGHXwAaiICpbn4U7xPnqb+jOtvPMb1L9+q4P9GZTh76beYOlw9+0a+AM+bHPWjtiwCTx6HShaOTJXK9pxPP0Vk8ehj/S7TLC+qyNyWIQ0f87dnHo4BHtnLqRSuzoZgl22UUMUkQwzgPHy4BZxpcp9nm9oqTyx3Vs1zwiTOjUTaje6adPJ0ZO1r79RZWNmJYYmfupJfSKehu8WTETe30GtYLKT3dcCnuh+GdW4bujnOW9BDIOMVYSGbVu2l9NmSqY5ciBh2fvQS134fhbVD9vcofC/eMsIuIwqbs59sw/tcaSb+LgmXOr7A+oFXAuuSNcOQ0ogwfkoV4XQEP0nJAf3e5mn+J72QPjKcQAclCSBakTT6+p7nr+J8VdHQKAnOcu93Si447TglmeaTvaco8M3LdoLyiR2evThRzq7icYTMbEfWM+uk3tzVl7Qx1hIiE4zNXRJuNfNacuZ7VoCCH3sSbsRn9cdwSTtM0jFfLWzBo7zLmL1/ZZn/bzHg6uu/t+XGLAOE/S6snKyZoy60Mfd0vGmLZra/iXo+YYctxZ0pEgoS8orgLgZkXk06/Rn3JYpDGF7tZKk06hhHtmn/mj0YEPSmMpsaoudhcElgeZwNF8yufreACgZ4EAmtYyTsyp1vyQ4FJqt2SuFkvXuWKM5GLuWQ7cb/0KFymiXdaDiA0tpuXSsRbTnXW0/+ZmIjhWrlE68SDVxu2mqr8iJQGzREGL3J/Jh3q7pzMcLQq9xHdl1ST9+MeuFCFXZj8m3ZS3gOaeqAi57zQtY9SBqJDM9f2KWiqFAEIGdpB6pbO6Rc1V/Y2VIxqhw4lm9SpOHYdcYDkc5njg2BBHP/Qr0ynPRqZhnL4hOyhyIiF7CsaCyN6jPkNW3/jffFzAdpVNPRoo1oE82Ljq/JvYfMULbEbsnkNt0+rkIXu9SS3/8BUYd2riz4MKJkQHoY4qmcMosTLN4RYt8+uPUfyR+8VC3CMsHg5I0Vn9hNssls3jX+8HXSyPGLRqzvMCU4IjkdsI09C7bSh9+zh+2hn6ikP/ekMLkQNil2ylAB9DIvmFvAOPq+y+IIBftJbLuPQzAJT0Q5lN61KPQHkedFmCY+BgtRRvT20v4NsBRz/r0sSScgZEsZCd3eaA/i4SECFDF9hInAuPFLoG09nLZ5W2cYws1HqSiZFdFCAUqoRx8fBYza9z0BXGV9u4/zs2mPYglCRHkwTW5PkWCH2u4kNzFNrv8gdx24wHS4ybiHsBdHGxXUwfF+7MAnf7uz0Z3V9fIHuRGdt7luFlOyWFtZT8lZ5SWpWdKSIApPulqtJT28+AI/AWuHwpzS8kS5ZZACwLZjs+sNPvpGbyDzXVJgIdE6hGv5KAQybRfdHl8ZM+4wuwMRn2pMODJmYrcdhPOF2RrzHWgSK/k1ltN/RAfOvKZVJDVzzL9V95DQkE+b4VOf251FYgnVB32mAVIL3odY5KzGFAZZoMNzmhPj7aWKQCXX0UslL6kRpNPGUkll2w5+pMI+l9uSgKMXeLV/AIZu9HyOCwvKFzFl3lRuKKXMUkHMdOzbR0qMtMGI+DrlKUZfGp/FsOOHki+f2hBiiRjE3xkOy6aC/9sJ/MyBAkn+SYaqmRpSDxIx5sQHPx4Wa3COdpnfPiK6rYo/GjPbPNLHGdpSzUW5nPdXdBEKD/VM9xUQC2LqgTlV3fIfHgI6iMSdmg37WtP5eJcq2CaWFFc0/Pezm2SqrKnYzB+GZWmx3Ux0DDpCYPCy67cgahAYISmzFoCAQ0WbXqndXrspQotnjhMOvLZwMBF2Ua6PdRCCSNU/jQqRkLWt9wzVjEZa1CNnGpQhQykS+82G3EOwwS3k1SCvfdGgYnQwZsZHvUcxQ3So3WP7I+Zdrw3mShM1Yn9+9sD8ub2+FoC/+6twOkMxVHF6Ghu9OWeLnR4ibvMweTZ4V9Z2CspgsWIS4dnbxlTtCWIVb9SbnT0fr3tzhITyuASlR2MAE6thmjXImbO+djh8tzNuZywQ9u9eA4KZkas8lN3kt0hTYwZ+VGW0FA2nTDEvPkMy+IyljF7QAuqXN1NdXjDqlMylAtDu/w8mt8kB0RHjc6kW+5PQoDPpvPmEsxKzfx5GTFQZQcHog7G4/nwkRGorNsABSXngm6KkgLCLTpD50f6yShIzEKczT+QK7sW+3jLu2WbPvL+YWKt0Ypn28cNKR6fC++RNkL6ScWAK5qy0fAPhiWPVCqGGBOgS3t6A7qiqDclJ+iTT+wL8yKfwhPBdItmN0zWtkmQHLOn5QPErx+MyabdfgRq/kgL5ssAcRqfvEmZ9MbbZh/IdX6fIgTuS4amOXIgYdn70Etd+H4W1Q/b3KHwv3jLCLiMKm7OfbMP7UOmG02SqDlllrlVOslnRTXkX1J/FfjZ/HbjpXD5X64Isw/OZQc4o1gBV+fvCp0OBgOa1AEPv+yp0ocRqQqy19sVJMEaOv9JF76cbFA5N3DWe7suo60F4Ei55M5qlS1HxBDJtF90eXxkz7jC7AxGfakw4MmZitx2E84XZGvMdaBL1GZRwWcJPyyBuVUhK8q3KH6xdYp98ckomZ++NIRlr3kwAGK+Xhd8I41xnVpl5V5MgL7nrEots6btbS3fWF9i8cpAwBXD//4cO31IBTLaqoKZqAFR80ONY1A0rq4FBxwW5D9/LLEk2WU5kQKPhuCv7+WRijQ+F+bQZbejXOLKagqD0DOKsw70YP5fV0lfBOvxCv66syy0T9nWFloBB2bAdJ7ttiuJCIJJ1Cg36AXI13nZJMVX2cuOQ8qdxi5Nv0jpBbXcQ1SaMdNa9JcjKD+jMGODK1U1aPUOMG26cxViikkhVMDWzApEOkslCpcvM4hkQzExL7tH8TMw2HDOO0MHZwlJHFE918/fxfMSOumU2r17cjbUe0w9FZaSmJa/w7lHDDV/Ct0fKJz4Yu5kc2Cx4O0pYH/t+cu9qHelOXDQomMgW8ofwgaYzhRuZoyZZU6iLej/5ao3BitTzR4ZpbMXL6IbiMuYtbw4NZSx5SL+hjkkzkN+1pqnNd+7hQfZ8O8oHxrEN1NJrSfLkCMKD7uFRTEA62zWgkg5oJrqELOSbPcF3lpf29mVeIxRfsY7PFsYTtyLK9AHw8mHU6/31XACuwRZXtNI/IvwhO1whQohHnQLYRVuJ8Z+kSARGYcmilIAm7sWbFFHMLJLN/W48grO7TFitmreTx7P3lS5z8gfMaECYCEytE8RsxJk0ATAedX+McwoL+WCIZCebjR4uWiF8Du+il6s873FSFy+ApItVBxPCPtJTXcig3HXMWsUyYatQTkO15QtKQBeJskXejhWO5GAj1CcNVvLplnN/jMii/T0wrW3gKmFpKpMiVnvgaWHszLf688SiOL1HM5dbLqCvUpFZVbEJb59NnOQOlSK9UF2UrcIav1E1YQCNYN00DpAomRXRQgFKqEcfHwWM2vc9AVxlfbuP87Npj2IJQkR5MFU/lt2dNfVxfsOgrgrgSTXYjQRZIhIjr1dd6l+Vew0Ox/8CuOKyhVHvVpOHHZCjHKYthfIvA2Pk+IKBDKddtbOogp7L4GjLFOBWF8PTST7znjuWI7mlzysW9ZHAqcnZudZyeQgv26jN+ZOKOUvbeaFMwYdiqePF+ifiAMcJ5uQfe+8FXaE3AvKIsXqr7Eu3ztW+TEt2svfHPO82SytCbA6ISc1mncc72+bpt6411ZiC2Cv/Tnwc7k33iPAYBjN4E2pvlnNiXqmwrcodqeO7qTwBhlnL1aoUUZrbM0B8lQ7oeu3Ukz7yD6vgTjsNlx7EVfcC/gOfap3T91Ihd9/D1h7jHhaXkxY9SVBteD25axppJrTLIhjjn8yIzaFOOJ5Pda0Wf464YjObLjz04Iy0hZANkeoOCnyQvk9hYES4AdtWX1jiqExUUTIl+tcjcdhFnX57xSQpMtUhVBGw11zYuPDQJqXCrlLXHZf4R4idYEpwyQ+j2y/J2TomTv4bXqUVNYhhTmDqVIs2/VT9g3hMzFcK2FXmZGqPKEh/DlCu3tSfs2u9/jY7NA5Cb1xQqz5Ng6ycl8dKjfPZtGpDEPThqyKqZzReWuCFCZUZ2Olt1/Xm8+tzfqmW6czB/ioFMLBbqeElYV+VTBCuWfR6n/jDiUgdZM6iL1pp+SCpwXsQMnHc7x6fiOJK0q3sj804nkwTvgB9VGQXHHnb2AnVRDnvlb5OswZmLNLCdOl2ltP4AQG93tKewcgGMBuA/ji7C/crFOiOvpsPLW6SJMhJTljKuHL6op5ysgbWQ8Ev1mjysO80tK5HdIeyfJhxWzzOOmo5bx6hic8vH7lS08VFB35065lrJlM3GWEDPWrzy3UGvIE4TFQZQcHog7G4/nwkRGorNtf1XTwmPE/wxfI1LIFnlbKOZBs6jlUxpxJtEIUXjR3ZLVhKW9bDmBkTyhlG/4pI/yeBVLoFgtDiZVvneY+lQt3hiWPVCqGGBOgS3t6A7qiqDclJ+iTT+wL8yKfwhPBdItmN0zWtkmQHLOn5QPErx+M1t7s/ZJI8EJk2QhHbEa0YKJkV0UIBSqhHHx8FjNr3PQFcZX27j/OzaY9iCUJEeTBVP5bdnTX1cX7DoK4K4Ek12I0EWSISI69XXepflXsNDr5JTPaedI5igB01P1fQLXnIbRmxngB+2SaREQk825RDqIKey+BoyxTgVhfD00k+86G+78TmqW/dKcQO0x0dAm/TyT64PNuMPwpCE3wkr1hv3fijun/QWlYXkSDnfBPI3JkRsImcq/ALtbjnlQXMA2+UsIhzjdgWPWKuenzcjr8wv9r7kYyUQE1rCbDHl96OoCI29Z+0K0KD8Qp9cXKONdjVJVjNWwfTGBi8TzQx/KEwuXh0gv2kbwWf7WbsNXMgoMz4BalSlr2LKvc53BHqo7Y+hhtysawVCnRCAL006H6LefEngLGbehnZGufhBpswq0TYMajfLEbLC8eAfi864HTr27+fmxBplcfwDOw6lUDW+vPzaKpC5WuIbHw3zL/frqEuychf4l7qFxDTrowvrk6F4QabDzwwQ5Yl3yVpC/RmXDtARu6fVMzF08BjTZNO5FHO8m5p0sqm7vzWY6ZrWpT9wRzvnkXnHvA2P+TBH9XU1ZUWpREyB4YFe1H+JW28c1BF24Njti5SNcBhGOHjap5p6nx2UtywtNNWApTzUvOtAhwdzRiSf6DZKWKTfR2eXi6ZJe6CPWVv4nluAGsIAkIlMC8S+sLivMpEP13q4qYG52xQpXK24rikC3Vkre6c+LEehRAJg9CVsDUbOkS2av9lCyykdZfWU0KP4C1ULQd0x8zESMou+jEPSalWehM8gnKJJCjBiMuuMOnz2/s71gmTv22jHdIldYsj/iyCY3Czu3WPXx4+wRMjBCpDy4y+GXKKtFNj+WAt5j9KcGKfviWJTe2k94LxKBceWic0tIHlTFitmreTx7P3lS5z8gfMaDWuiuHGkyGPCG4sts1kqjCzeXXJ9jdNYwMXX9ekhNgzNfdGuWxxzPt8IWSKLic9KRwfgAMaaTeNqK4XvXik8E0ht7+gvpG65JG4ABgevRY1Ayt5p+Aua6+kBbo3p35l9X/F1FCi//HlB1M+Yvq8F48FVyUAzvK/Doq765JoyuzKMDbjwTbVb5iUtRba439jvigI5cTUgPnOFW7IBwf1btZMwYdiqePF+ifiAMcJ5uQfe+8FXaE3AvKIsXqr7Eu3ztW+TEt2svfHPO82SytCbA6v2W9CTEF7XkJCqjOAGoMRb7WljQOnatQ/GQrWb2BA40wQchVUBKLHcdG9Uoj9f7l7wY9LnHeDSj2irJWWiNqueFfOJesET3uZlc5CXlSyavWk8l/a/EXYhZHVH6UW8eKg9AzirMO9GD+X1dJXwTr8Qr+urMstE/Z1hZaAQdmwHYOBd0sB50nLp4qouX75M6tJyeewXAV51WrcWuQdn/o3HKMEGxc40QLEeqc40HAnllU5wra/k8qeuYUj6bQGksxAmhMye9gN/rOoQ2nx/91sNRMbIrk+TPxSg/vyDQV1vLkP38ssSTZZTmRAo+G4K/v5ZGKND4X5tBlt6Nc4spqCoPQM4qzDvRg/l9XSV8E6/EK/rqzLLRP2dYWWgEHZsB0nu22K4kIgknUKDfoBcjXejlfxWm8YWOf+lq8NGlPqd6OIljOifjpyY8bwGiuUGB4Y4MrVTVo9Q4wbbpzFWKKSb2x0gbHgDhojgiql+lbNVM1UeCGEEgD6x1A/dvnzoFLdR7WvLmqViJFLI/t5Cz07ZZ6QnwZZDQtKX5s77k3g4l3hA2vCx/oiQ6ajd1i9slbh7blr3LKpJbnT5YRFHWQ0yWfos4wZNpUm0N5VPQUrWI4H3hLUG6iLXe2aGdRaKZ5EPaFOAXB5gDpgsOkCErli3GPMDy02zittqg6iE0q76cQOCVF4ZF1kK5VXK7Wp56TFeMX0BLK9cRVr+6+TntYrkWi1ESp7QoDsoRAHslU+cL94toXkOpsUx42epIL3kBPwSIDwjuQXZx95rT7QIhzMmk5njoZR2sIx7kM7fVCmMQkv5mIIqy9aOYQd9kpqCgNmrU/VkPZiWiV7CSQsX1TdyDS5lRKylBaSZ+OXcSgq9DX3Rrlsccz7fCFkii4nPSnEoDkpOkdR2XBIJxYy02HkPkFRy4FgLJqZRFo8KoleARLFNBH3M/lhofWffobhkAtTnx3D75bN3nBlWGvSLfLS9PbS/g2wFHP+vSxJJyBkS+2duVIfLUAVAFvxxzic+aZYu1O+XRI9RZaeJ4hu08D5E4zNXRJuNfNacuZ7VoCCH3sSbsRn9cdwSTtM0jFfLWy0bzvoBHWIypnykpiuK/yG+PVPV6zR1I74kAWJM6dMUozAEObdRXgFGpLakcfAyO/+8jF4QG+v9TXilYjaZ4G5Y+D2zLllqr2/jZceDKW6tmWveBdipPeSYviJ4NVPN/ZFZPHoY/0u0ywvqsjcliENMnhw4/IOZeKhPIwzLGj2cW4Hu+2uka/ktmQoNknzZkeBYZ5BXhIu+KZenpL+BktcobwTa9o8AoDxJ0z/5VFDyH8ZUQfk+dqbJ8gfX6l7x8Bmzs3p4NAyjLB4VfoUB0SiWwfeE87+LYjdqgegQXZBe5msPkjzpHQEP2IprBZPsYwEbHy5UTKDuHqi7RrfNP7LHzKgPdlOdUpyPZ4OzmlZTnEH8JtD0AnnO02HvPCta+48X+ln9/vkBZErENPux23sRv2g6fsZMwed4urTQcEbV6HEi2W6D4CAUX0rIZJNLJglikMYXu1kqTTqGEe2af+aGttuWK/2FY331cm0VgurXno5LTpHMXuAlehh/hUaKLKJrMRr5BpuyxdXzfCmxAvMCSMXg4fxAEUeQkVrNfy9hfp72biLPtslmAfTbrDKjiVdVCjXaUcB/HxA/ThOYJE+dyzhf/gcU3ZJTQetiCCDaYslHS+h1rTbmgJY3XpleKPvKlAg99+qnVnvN08waSSjWQI6ukJY19iFCKDYUfedZmChi/K93kLq2ioO9fVr2y+EwxAu5WC53fZUNG7ccGqfu+1lANZMYY3ozBGmPQgaqhkIs9Hoxbqke1wqRatcwQiOGEZoFmKNjUHjbXm9l4csXPnQ+egBQ0k6MYTkmaj8F2P42t4J5zuaxyNRkzeC6OzQgLHcdD+lv1YCXBB8hwd8L4ISHf1HKjiLnRUctxfyaOC2H6ZP1ef9qfQs5jvkcYuNUZH4A37uPZIs8o1RbKnwB7zKgQaZFIwNgSf0LpBwE3F/Enh00jMbdcxAKFj2aEvNrvK/vXShTV3TEs5Hu3z4aMoxcjgtHQRzEG1NeHjD+Kr0Dg2o6DtP8JsQYPDXjhoaG5FJYXXLkdgvfGRjiTJgowwkK7bhT9yW8p7iNTd+KzHe33TQgFLCBf0a1CUPm1rjrd5dXgswX4V13Y+pFjBePambztXMV1pJJ/yVDPNytiggU4QZ8qV+Bm/eHuaTEIIW6gV1aTW5rCSMxtlokbMb4nAWt0uAkv/C5v3Mc/KMFrAHdJ2nzbD/QUmWNdMIMV4Nc0JwoKmu4jnO5T2JU3fERNgxqN8sRssLx4B+LzrgdOvbv5+bEGmVx/AM7DqVQNa7YLgWYjHb56RMFUbSeeAHEKbz207NBdR01ib99a/BC1f6hhB8Qs4nNwn289wtDeBmtp4R84723tcZd6uFx5x2NLSVnQIVx2CDSnLi6LqZopQUm6ljD9CdfkOI6HsyNzlp2Z2Db8Fe0pvboCQ/sMasoPXHLPRV9QiwsFxN2tXNJGAj/ljUHs8tE+XwMv862WcLz9/DlgKZD18LKuAoGn8l1YGpa84fORx8hPnkB6tDwBUq6It1ryEbr//9CORIe5Zrc19BJoIjWTQjLLn75ww1GHeJwMhX5qwlBsnZn5DMYzvhBJYUaWmUmXOasn90ac2nxEUaonE2jQZYVafvviWnOZ264GaXNCreD1cfgaQnCF65b5h20d8hIt0zMZemudk5mSBEuBjZA7j9WCD1Dp37hWrlE68SDVxu2mqr8iJQG3LZJxOQo2YemeuNA4LJ2YpHdl1ST9+MeuFCFXZj8m3ZS3gOaeqAi57zQtY9SBqJDK4uY3IBmEmPGVag+DTOSwX/CHrUf4lrTbTZ7hsjZM3wEKfYkcApj/LcN67uxPE3G9V+V+lqOjT6G71fkEx2aUkUuZlfIdFl+lpNIoXh5r7vEIek30R7WbBe6JnPE/5OQQUEedRpuNgQNASjyXZx/Sbfdnz6QmR9kB2rJbck87NRH7ywp7BLja5cxJ9aBsQJLrZwivo6YMZDYZCLNxKTmcbW0UZTrEvJw93ouTUbjjueYdib0eRJaifEzvf9f2jiJ09TVH1mG3ScFvw3PQl84y1HxWi6jX6rj7mtwoZRqeuj6OzjjoM3I+5W9/YKr/VIWR85Bh18AGoiAqW5+FO8T56m/ozrbzzG9S/fquD/RmU4t70FU4065T9rnWKPi80MTz2ykpLdQ7yfwYO2UfoIa2ujDCQrtuFP3JbynuI1N34rMd7fdNCAUsIF/RrUJQ+bWuOt3l1eCzBfhXXdj6kWMF4cBUNTawxqVhATJc3sUbugcSrL08/9by/VEU1ymp4fBWcll2orb02ZB+CatuCj2nvwykuI5dPnaI88OJQBd2230cJ4lkgkEOI9bq4Yb2QEkkzBh2Kp48X6J+IAxwnm5B977wVdoTcC8oixeqvsS7fO1b5MS3ay98c87zZLK0JsDtnaGrldEJe0XIM3oMP4BmWS2EAZFPtMprE4Hp4W/WerCdhCsCNvEYUxJ6CxQ2jVcgGGWcvVqhRRmtszQHyVDuhn5v9GaOZaXjeIzFagZDU7cHknL/gcpMBr3K49mMo25qaVQR4Bq7OqH/j7xGF01RtpRBh1ju7lhpKmVGAK0sGpRiWEDZ668kMNz7QNfuJJwfUJ+3YIV3P42v4fwqdfebIMV2x6/u6/k6o+1cTVe23m1j7Z+wX9O+BBo+gr8J6Od+XEpi5ZxtozJNUCSgJNPJPjKiNbFpw1J5gK7iMgLfia/rV2+qQYVlWsV8yMd1li/NITeKVZ5e6l01N6CGz+ZeMi52F/+w1W5iTveBUFjlO8UmUW7IsbPVlW/tBE0IBXEnCZOxRmi0tEZIKAj4/aUy9tPrNH04yuff84tZDY18AKysTStqu0WwH7mqLmRXir7Lluv+QLJRHg+2NaQDnsdgA8G/rl/tW/yiRW4sotCMkAAJHhictV6buJs7uqp09XbFzVX9jZUjGqHDiWb1Kk4dh1xgORzmeODYEEc/9CvTKcbgpUNghqCVdvhMxR22uWTN1Ow5mIyLvDAxr7f48Rcp7GSbfAEO9wal9DhT/D7vTUPMA5zyOFQcvSXll8rP2dTzNkT0Dmk0qbf6qgEkB9wRZ67TAiBGzGxR//ulU/UKO+Nm4cIpk/JqGa3IarGsDaNySIlCQcDY5SofhN1M/ueRtURTRFD2wFrO3LgchQmfGIasmDKXiTWwlwfWydbQZjDMrttMk/e6zMxKfwNXdjvPqy8bBHU66cFWjOWOblm1uLHzkGHXwAaiICpbn4U7xPnqb+jOtvPMb1L9+q4P9GZTj9dCtlRXEGQlaq55ugD8YLneFe6XGTWxPsGY9dO8x0MEzBh2Kp48X6J+IAxwnm5B977wVdoTcC8oixeqvsS7fO1b5MS3ay98c87zZLK0JsDhsPQN2VjTLcF/tbCqNffniUDOuVTBr0nXuLAW8qkXEYmNJPfOfdXrqjnX3vROIQfYd93bvCK5xO4dtsExQ3GLZ1C5m0VxRZrZQ0EAcNkZrBZa94F2Kk95Ji+Ing1U839kVk8ehj/S7TLC+qyNyWIQ0yeHDj8g5l4qE8jDMsaPZxDykWaMUQdTHRK8xxM99J6ed20tsu7jrNC8/IeNAaekE+c1/mGRyZev3vzub6Xlg8fxlRB+T52psnyB9fqXvHwNdZNiyI3GKX0uY0qjWWMj2nci2oVnbAear2aTqygV7s0rVGx8rwmSPeIteUCG7rr0Vk8ehj/S7TLC+qyNyWIQ0yeHDj8g5l4qE8jDMsaPZxmQ9Tb90uoqX6fdaw9UOLIY6KJTkMqBMen2VNj/RZxatAyNPGhCteVcJ3t5MsvETcYheJjgLmY/3YjHf3JvV2JPX/MS6H6sRd7RS1oObi5bSaxLu3FjKCVbvW/174km28KXaEt+nmJ4cXiROPlfES1el4ZvYtA09MW8OeKKc4jLjNmYHAsEBITLmP57GvYycveaO5XQW/V5V/7nNMwk3R+SAFHGsy6C1GoZpJ0EMM7Pdy9LGOTJ5SIOpc3Gz/ueIralVZCeDWCVJCPpcf7alms7F78V6TdzKD09eB+ZSfGmBYA0Ptwmr/sm7NOWNKXbtzagEM6Zl8UMTuH+LtivdkYbekALefnwOCFc8teroKzRcfaF9tX5zmF9lg50wgye7so6KM+ErsOUB+u0uNSPwJbQckrIs8bMVj/InhaNQ4RFuapBym7QhoebxUHZmI2NLQISl5k/fz8EjGMbabEpOWSTBviHHZrQ4xVS18+x2VMLHDGh4I2uFuzuratIYX2cgfNfdGuWxxzPt8IWSKLic9Kbhzwrh0XOo2ZoYbANDWvx3PcO66NlFnkiNYscmGXacnI0B+Mx0C5keDE19CIU5q18TMcwr+swtcuwYYaxhlo3wFVyUAzvK/Doq765JoyuzKE35NpXq8gEbOLsKd1mh/sExy4koTAKihH80b6+18/SlVbDQKcNJdr3AxfQ623CE2qY5ciBh2fvQS134fhbVD9vcofC/eMsIuIwqbs59sw/tQ6YbTZKoOWWWuVU6yWdFN1uJHbCrjjwsRLqdIXvtxnW18RZNpryqoI0yzFiWm7ZGFrhE8YKsOTbpr8P6CGTZzv6ImevUbINawIaurH8/sy5CZ9IM7sWTdIdhW6cu+wRZMwYdiqePF+ifiAMcJ5uQfe+8FXaE3AvKIsXqr7Eu3ztW+TEt2svfHPO82SytCbA6fvS9u5RQJ5i8RAjVoGyOQrTSrzAt/4LCbiOsnwQjtfML+N3zAbS3MNuPxeMcmoewBhlnL1aoUUZrbM0B8lQ7o9UGRyO4n4izNg+6TR8DhZ9D2OTol0cU6jPU88JfDYWDL7yoVYFRX950yhmM0RIrGadmdg2/BXtKb26AkP7DGrKD1xyz0VfUIsLBcTdrVzSRgI/5Y1B7PLRPl8DL/OtlnC8/fw5YCmQ9fCyrgKBp/Je3fsrKd47CI64wkwqZQ0t2JHYw0ROFO2G96xrqT2+kDTVBoKkwvfBO+vfseQqxUHTiUG/QsdSC6SrydgqcVKEs+XnmIAaLUgXjV3c7O8DHNvn0ce7Rb3yLYdvtqM0gLs7nY/okeVjB7tgbjxYNe0U095JR6lLqcMsttGKLTzZ4CAhwdzRiSf6DZKWKTfR2eXi6ZJe6CPWVv4nluAGsIAkIlMC8S+sLivMpEP13q4qYG52xQpXK24rikC3Vkre6c+LEehRAJg9CVsDUbOkS2av+RCEyMGRGyOqsOjjU8gXXyx8zESMou+jEPSalWehM8gnKJJCjBiMuuMOnz2/s71gmNDQ6pWZRdQlKJz3vnTaAyIYqF0ftoIXiWGIermQRS3J4Vt3xljFBLoEoLAV2430R6WClgjtTb+SmLGPWfRvntNXUkiD2nRFv3gfGpjZIhHliPsGR2rvOMP54hUmJri/Q5gGXE2ZzKkoTCNpQRvjZ8NfdGuWxxzPt8IWSKLic9KUv3JjkE+9wOe9qNoNGWfN8HDx4b+V4XE1UBib+8dBL+gttsZLOvHMRUobf/0/IT/tq0NJcqFeN4wp9tfxtx/27e5ja5uRgUDxZNf1qfBhEWXFDBViVOFqGC5cFEL+sXbAy6s8zGKRICYpKQPJamtdjyBTRo9nHZl4NrAIdlILUkqY5ciBh2fvQS134fhbVD9vcofC/eMsIuIwqbs59sw/tcaSb+LgmXOr7A+oFXAuuS/C2SwYcsJrP3hDFZwqibUI2zYp+a1zCeOqgip7Z1ea7tqH82GP0fD/QfiEc3fUQMVnA5+qfIfX47Btr+v9fywGbrBkzHWD7ZoclUw/hTtvyg9ccs9FX1CLCwXE3a1c0kTEJ+QGKF0nHq09/DkTcAUTsa7ZAvj1O/lqkXAHwJubygNocxne4CTHX3Ez/a9lpyhY9KMv97ncqt5KOfpRMgW0aZMd1j2lExVYL+9FlBJjXp67xDf0KvzSxDsFd5LWHZZCN9kUFthQBVrmgNtI5U9nxYaRa/2/OtzZ+u0c4s8KFsmD1Zsu4+4zsVA9rkUrh0PeSwKip+98UmYVn8jyIW4RHIqKPSO33v3LLmIz6g+qN4vlHwprgfHaz0G9d8R8vSIF4ZEEMd5Tnv1JeoYjOf+wrDXb0RlTN3ayUq1EzTi2KLG0+7z/wz6dYMYphyxLbr9CAMnwJ/qYrPXg8XWH9hIZH0zqZEqqLKoQCbhRE7m3z3ZWCspVg3EA09/mXOhFz+go+T1pdOsQgBfEqDw+hxVwNqzU0trr60Gd+zllRFU6AZqU+FI1TfaNvIu/Af0J81kMrHkTxMWsFBMelH9j4Njf7EbpIQLjLpj32sYShZlLWyHOJeOZMhFRtNstFjefRvFgf7TW+4GLNWWjOHvs1WrJjYWyG2U+f0QfYKoI4rQt5LvRIsVxltuhIQ2fMxWLcOVu/6XNWKbeBkMDGXgrw5dpyXSd0PysjpxmlEn53HBGAKa9atpyCJVcB6d5Ze9opkTmbJf9c22v5T14l51N07TYP59BKr1CEXCRs9691EdfNQRQfwRXjiczM0QXMTwsU2osRCKmkQoWQu4d66kfXV3Kb+9p2sn6OhVowL/TKJtRSpqd2GpqcugYEOQIZTPEYuhP6fs405Rpbksif3GnSv3GM1UYDhshxORb2ujQcDheUn+fQjBwObISdtmLs9oFCu2bl1ZrNw94Dh/JOJiQlxL0Lj4/MeQqcwgDC1JSnRR2tmRiOfBoW1u86rHxckBf2LbI3JljVlPR1Co7yinkchGaD1xyz0VfUIsLBcTdrVzSRMQn5AYoXScerT38ORNwBRHXj5cg46Ur0J/rZdKoxWbuDG3gMHbZy7Rwq24dm0WsrLA22S/Eg8vFEuOk0LOo7VAf3rhKlUYXoyl1F8b+vX7HhOBY5QIf87BUbdxhhq6zJivSn+6td6jD9k1Ll+v4A3qY5ciBh2fvQS134fhbVD9vcofC/eMsIuIwqbs59sw/tcaSb+LgmXOr7A+oFXAuuS0uvXS9xE2Z8s76mner498z5wGclIvprwyv46TDK5nSPipoB7GOtloVjp6V/OT5sRfewGlTyfho9zmb4g8rxKppJbAkz4Vw9h6ai+YAVy8TKZrD5I86R0BD9iKawWT7GMBGx8uVEyg7h6ou0a3zT+yx8yoD3ZTnVKcj2eDs5pWU78lcBHHOi56MOWfbxUr7b+B7Kl+okaLOLok+IQvFjIK93XSCgj3BK40hn94gs8x9brkXo6J6BvoMQ7Icl4rVyCtc9cV4I8G3fjF0mlhZmyha3JNSzHOiVJ88ASiDzfjGF0LN6tFRrnbaW699ZaDPqQghkkmy5aAjwto3mvpqAjeUFofjI46LrCWJI6n+mcyPZBEK05t+2PczanN2Er4bJcnSva3RG7omE5ILBNGBVnPHL0sY5MnlIg6lzcbP+54itqVVkJ4NYJUkI+lx/tqWazsXvxXpN3MoPT14H5lJ8aYAbMP2LFuFZsd/5qU0/8gdVMQDrbNaCSDmgmuoQs5Js9wXeWl/b2ZV4jFF+xjs8WxvGCSSCrFPvW04kcjFLEXc0eug0hlle+GTbjgmb12621r0NsWxxPOdo6vryGgyF+JFonEkHdqFfD8Y93YFOwv6AgefmI+TUUD0wn1OWJFWGHMFleASLV3vuF5xGFdPnq96JfP1Mo1b7Z6JCQoppJoAhN4Yvzzq6B8E1FXhAwEadNLPsVaLI1aKU9FQyINEYwR6TtaumxR8samQZHuGa3VEVwUUpG7qouBZfQREiMVxtTRysvD0VOYO86p2YUy13tH1ypio7eFXAvUo5dOjYCNf9kHSccGZ6v2CeeQQridQY3i8vHGr9qmxJ8Sav95mZL1bYsAk8eh0oWjkyVyvacTz9FZPHoY/0u0ywvqsjcliENH/O3Zx6OAR7Zy6kUrs6GYIM7Y8GJRCWmGLWpnSQrrP+vQ+3Q/oS3qb20rDZM5c1wRz1waznC3FgkG7HsN+vUAWGJn7qSX0inobvFkxE3t9BmGuOZ+GlAM1VsaQr87SO7q2Am2sCrqs5wMkGCWZmADQQybRfdHl8ZM+4wuwMRn2pMODJmYrcdhPOF2RrzHWgSK/k1ltN/RAfOvKZVJDVzzBIA4DgzLEk4kjio5ANs6gZG1lT67BClyo3MF6zP7VCXMchgXynpDi/rcQ1YN5c9RaZQwFfq6qriEaVKmd1AyzLTu/b3FT9qtThGOq2FDZFDf1XS4hune1t422M8t1vGf/AoIBvHzIdVUY7pe92SI+YBXGV9u4/zs2mPYglCRHkwTW5PkWCH2u4kNzFNrv8gd52NwMetabqUH8Hd2mcImJFc0HmZfwC5Hv39bd7A9bMIiM7StbJ+vZo/iJZPfCFnKdYeKPe6fuIQpYrhkDcXb+XAQGIsgm4ln7mNOUjvA7JC2CnApiMgN63dbMIouGxMguEhSMgla6uZDi2RI0BnEYSaHU8Z0jnZ9+bF25Bud2QLrFEvuj/4LiMO6+C3t7VILE5v0mLBy9YELbZLiP5mTX2ukWtzG9p1S2BDsl5HM/gQyWfos4wZNpUm0N5VPQUrWI4H3hLUG6iLXe2aGdRaKZ5EPaFOAXB5gDpgsOkCErliWJsXDKtHU7F8aQx9eWgPrsfMxEjKLvoxD0mpVnoTPIJyiSQowYjLrjDp89v7O9YJOC9TaUk9kSg5+igm+nFFJ1Yilb6FodlqwdyGTu/R3Y70YisFVJojv1HbJDN2vINXWOfi3ZP8POiHHOZr2of3mAS4B2+kluIrPvEaPJmcx+P2PKH7PNxwWJrcM5NJ+nPH58qvfU0Hjnl8lUJ/MygMUG0/fF9crcV4jt2rCJw2R/Di4fhowUc93nyySAZ9xhm5p9XXynr9aollG74GDzpT2lxjPeXb/l4ts5P5V+HDBkjNAVvPAjjq6y1fzlvvtYFauVnexNXRTI/CghKPQL6beYECLfPBm3pCaskRxa5Wh0TTqRrWOG2NmvH7auf/7OnPBGx8uVEyg7h6ou0a3zT+yx8yoD3ZTnVKcj2eDs5pWU53lPe1V0cB8lxQRiWLWe6NpLiX/C/p/vT76r9fJRoI488o9kMcWQlPaDvpXPh6t12ogp7L4GjLFOBWF8PTST7zazJP1kIqd84fuA/ajLyMyQ1zQnCgqa7iOc7lPYlTd8RE2DGo3yxGywvHgH4vOuB0PbxrbmW1KkBqmRD7iiDLJX1Vn1HL9MAZHjmlr4kwb4vbXEPFx/5lYdSTR6sYYZFij+gQHjzmBgzB8O+WPSIt27l4dIL9pG8Fn+1m7DVzIKALfSN5kLanmhuIP8SINImtcTYncDcswukoY/Gk08Xia/lkYo0Phfm0GW3o1ziymoKg9AzirMO9GD+X1dJXwTr8BvDfcA42NSx35SKgIsgoeAQYmSbsj3buL80UzGEXaF/WhlyNjGwQS3aJxxHSvY6ILH9cmKMrR3WRNbuwEC+Gqe/1nmQr+A9Y4Q+pGQvp2XUcMsu34WkktkqaA3PxY/A4p/VbdOOViA1mU9rqB0j5YZ28iNjdS9V4wAh3KfoIFUtBbwlPGdUJ6RfPjE7FowlD/V+UtPIABkUKyj29FVCsyj+//O9jZxj/V9VlMfroISelEdihBERX5hfvULcloX7IgxghIZNEkZX50axZXc+FE9m1tBYlmSp+naPlEAIz3PhSzJ02etz2Hie2oXX2XrTLFjjAoFW1eO3oml8taia+fkWZxMGAIzRCxSJneMnWw6JVoIlg6wiS6NKuA1BIFpuKM/mdxgeCnc9RsGte+3lEQeUTmq0klSUijtlpVpCLD1bUSmJ8zEw7PyljkCI2ip9chmz0yWxfy5wsAqxY0TOIVcv8osr8KFCjauOtZr89Riz9W60FumpDMtFfU6/gZqr/MngGp3jtHrXuqnFKrl0h/rdlrUfjtyGiByGW+AgCF5xejXyrtECh9XpeM9VoFy+PndA9DKmJ+VfxvuYTXD7bqewXw5a53CjtIJhUU/Z7DCofOQYdfABqIgKlufhTvE+e5cN/UAuUJ5gZlJJTIDqhrSae3gx+oQoBiaERI36iMfXAIARMKnft02lW+2+FtzO0D9A6kdtp6QfzSiKeziV930TYMajfLEbLC8eAfi864HQ9vGtuZbUqQGqZEPuKIMsl6mqM/dSMXqgkY7pRlNjEfu4SPP6UTboipItTI0Ob+34YljcffMzw+N5j6akbiFbSQ+2263uv1II0SSr9vjRUsaw+Tmrej27b7EQ/6OAeXBV56HCGLLD/ZUpZQcAnfWANMd7fdNCAUsIF/RrUJQ+bWve8Tr45tdWwka1tmbgjbsCTKiWivpjHCeU/ttD5cee8uNpb+o//NJUbAkDW1TnohP5BGtb3mOJqSoWg/BZ/1EhVOcK2v5PKnrmFI+m0BpLMMyNnjKt485Rmf9sxki3K6nwVbuNICLD9UfpbFZDdEyWwimP0n76jB5aHfmyEClt/4x4Wl5MWPUlQbXg9uWsaaSa0yyIY45/MiM2hTjieT3XEhlnaf32rbMSoqX9rqrYb+vPzaKpC5WuIbHw3zL/frpAPWte47Vb1vbl+ZszYNnR4gDJ3U+Y90V2yUjHkSUCIHpsCcgrJzdJ9YuItg7uHhkLhiMBRGhhHqrjoUQPrS4Gn9Vt045WIDWZT2uoHSPlh5Mmk0vqDfeLOB2AF+AtTzgzd1jvPcvNHymPojmcIDrz9X5S08gAGRQrKPb0VUKzKJ4lDspKeWYqmgzRCQEStdaUR2KEERFfmF+9QtyWhfsh7qX3Rqc6UkQTqwzSGkadeiaiPHSd5ieUqKaXyhldnwEsDiYqE5EJi1LcnUL25xMucptRQmUSr/Ih+1Vay683sQ5SdrKh3lRypNrNIMGQB/kmo0OaGYacIglvwSopmmTlL+IX6qxUm56HjpVj++CS/H0722lBWs4a0mBiaYvfBrl5rYewdpxkcmYkKD3swJ2ucJsdpgzdnAIxBAMeTSHMFmwEhimql+NAZBKKErFjw4MeBpw0CmZZOM6MC83XbVyb+xG6SEC4y6Y99rGEoWZS1xHn8EiCAG9wMfrsxG8OftjqryiHxlBnic0rD/0Abkt31mAdMJRrH9OO1EoulEPdR12kFSawBZ8HO4zRjQ+8gRFv6QwmO9ZqGXhctbINlX88uWB2nJ55k8qCD318AUMAXTDR0FFpv1xOgQLf3H5LMiCLPSEXKTXyelGDmmOtgFkeD+Mu5MQblxDNOcuKyY6F+d1UmyGr0y4QOVWyJPhURV82u8r+9dKFNXdMSzke7fPhIa2yJupZ7nJkbbtQX6/bfoESD8ofMMJADRxDqsJcgmPZLcR0Tyv0uiojYcQnOU9We8AntqRdzoYwShYUCT6To9aTyX9r8RdiFkdUfpRbx4qD0DOKsw70YP5fV0lfBOvwG8N9wDjY1LHflIqAiyCh4AYnHLtjKb1ZxgZUwY+yXpERClawS3Ydbza3wDz2ULm3ZPUV5PSeNcz0IJ2Pg6R3qNgNwQum3an2bjlYQHuTPYPZWcuRJ2xgQpX0iqZokm/ZqznJH6ISI7xm/PDLauxKobJg9WbLuPuM7FQPa5FK4dD3ksCoqfvfFJmFZ/I8iFuHUvsH1BPvqOh54llgqAUsCKZ+lCvNCfSjtnkzo8q489Bpx0VnYPAfhbjrbc2I71KlITvZty8i9C8qO/4Cp2fLU/C4L2TYOyHUN2a7Der9gKEqMtMGI+DrlKUZfGp/FsOOHki+f2hBiiRjE3xkOy6aC/9sJ/MyBAkn+SYaqmRpSDxIx5sQHPx4Wa3COdpnfPiLau94HFfdh4z70VkAgoMgsAxVjD0L/Wn9lTasi1Q8fL8hJfGN1y8BeWZQ4d9EVr0EOKjm1BM1mJLuEojiAW7GKTCMz3kxFVRX3My8985dI2sdNNMm1Z2m4noOX5Jmo0sCe6/7v81etbxHvGHzeG2pmgtHref97mMDA6Z/p6q2zsaHhmtPVfEExOL+auG+wycpa/RwY6kmPDLJ0ydoqN+0qmLFSjMoRig94XHpnRzmt1ZuDrMDpS3Xt+H2FtDIIbQx1bPZaOlLepscslzIJoX3QFjjAoFW1eO3oml8taia+fkWZxMGAIzRCxSJneMnWw6JVoIlg6wiS6NKuA1BIFpuKbuDtrxVsxLTqBIX0krmr5e+ZwvSQet/1gPVc5VMcNjRkwa5r8uOToMefXpnLatuZzvM/5+U4SKEtiFXkKk3V5RyPu5MJnQgemB+o0hHWfVP9W60FumpDMtFfU6/gZqr/aTNGkFu+yCTMaIMjoX+BMpUtYpvz7OZrjiDq/73w7g9ejXyrtECh9XpeM9VoFy+PqkmK3R1FbA3fsxSmx1PKnewXw5a53CjtIJhUU/Z7DCofOQYdfABqIgKlufhTvE+eFfYj1z+YxWhYWYCvBnb4DemurCxWhpwgoKP81vimJ69ANrTG/U73HYU+u+5Z2sX9TMGHYqnjxfon4gDHCebkH3vvBV2hNwLyiLF6q+xLt84ZEg9gT01+aq8A6Q7xDQdYPPlfOFsvNjLvUTB8P46+z8po6SdDkciH3ml5xOVgaSiSmVpkDhh3JjX8K96vGrqvX0ZopJscywfcXbhy2gNXsjRRPl0Fcf1fipdxiIJnzaIBXGV9u4/zs2mPYglCRHkwTW5PkWCH2u4kNzFNrv8gdwZFCXucSRLplUdJFdJtW4be+wyI7J9qkLHxS1KHNdrTdHLuyCu9+pnlGv5uWwCC2V41XQlyowgRVWfI8p/HtFrmHbrZ6wNsbzjn7svJvJBKVaSTwvrjmzYmUkdNyE7BDdpKwdUCrtoY9DqYuyzIVY4usaPnm469vNODhlhgmslofFhpFr/b863Nn67RzizwoWyYPVmy7j7jOxUD2uRSuHQ95LAqKn73xSZhWfyPIhbhEcioo9I7fe/csuYjPqD6o0RDhKkcexnV92+49ct9A9nuCyFfn4qaZl8W1TY13u5gFCgnaXpadznvhikae5sNK+9aq7b19hjv15a5PvT1AGrVStKEMrdIvbmpBVC6pME4TCMz3kxFVRX3My8985dI2qykYSpbqoEQNv1Mwj66bVeLg7JTzh2cH8YfKm3aHltpgtHref97mMDA6Z/p6q2zsaHhmtPVfEExOL+auG+wycpa/RwY6kmPDLJ0ydoqN+0qmLFSjMoRig94XHpnRzmt1ZuDrMDpS3Xt+H2FtDIIbQx1bPZaOlLepscslzIJoX3QFjjAoFW1eO3oml8taia+fkWZxMGAIzRCxSJneMnWw6JVoIlg6wiS6NKuA1BIFpuKS/NTPzLd5XZdHTlB6V56FXzFUeIKOsYdle5i/yxqHl8vSYoTyyEkNeFTNY6fXor2tk2jAUotY7hB2fWU/CYKrRtTeQlFgORLQaDXBCP6ykcbbbsfwhZE7+1ezF4U4fB4EX/QcDmfwNx6CIiyv3vaEIG73D4LTJ2tlYjCLt0YOnRISUw8DAApjZXrlNaR3unJjcV3n1QFyaXuFfBv693oVjcClX5vdAPLvfD15d351bTcWKVlygNiIwlwS64pO6TyRz65hIzRNaVxp7vS5RH1e551PQrHsbL07dGkwHrlivlP3mC+eXxK7He9N5MTfIL1RiWEDZ668kMNz7QNfuJJwT8HXU8YoY2Cjko6ZIddtt+3iH8xVwBmrE/dsoYu3D5P6EyaPSersgfNs8MzjJPmGnzT2s9wQR4Ml/HJxbyXVz3X5l8yndtA6YhQdQUYUJxdh++xQpjHtNO0hZWz1oTcavGohFh1LT6LUzFeKQp1XyBP3mC+eXxK7He9N5MTfIL1RiWEDZ668kMNz7QNfuJJwT8HXU8YoY2Cjko6ZIddtt/Ori7KhEoIcy2R6Xac6p9BLyFsuKLa5510mrKKAABmW1KeGqeUmsnYS/MXgMlm7Xrc0gUovHopqOD6EGbNF8drb37D/7mn4mlR7f1T+XOjOaX3UTIrxTJihar6mwjEv/IWz4y3zJOOmnquGcB1sDMJDY7TEgrQ5v/mWexV5o3ps6mOXIgYdn70Etd+H4W1Q/b3KHwv3jLCLiMKm7OfbMP7ezU/BkQ0iZP+Q7dyYeRWDYpUBlZqu9/1Tl9RDqkkV1g1N56RYTq6RINPP/2C1ud4IZDKtEN0dJRrayPfRwgpCWWeljxAd1DmhRsWRYy68Z81F7+oQR17kfiz/Bg8h9d9KiUgowvnZEdx8xcRccLl9I3NsnXcdFaZdjElmUwfAnJK2e/VAtlJYsKOHYpHA3ScbNwaGZ8BSIf6cF8jTsZxvE5v0mLBy9YELbZLiP5mTX2ukWtzG9p1S2BDsl5HM/gQyWfos4wZNpUm0N5VPQUrWI4H3hLUG6iLXe2aGdRaKZ5EPaFOAXB5gDpgsOkCErliXJiL63LWN1QAk7G3HjghuD8ajnBVjvelBiaHj/MOzbmjTbEw+sy0hz/RCQ3Pt/vN4oCO66WtYXF1WvLLFiJisjKejP9cffbxJuXXE/sHKN57Ppu4tufgfCJoOc6oc3ldU9ikfGgo+JWmyzrrxKxlBGXcZt7XNGAytZIjj3UqObU=
//EOT;5004439

        /*


受欢迎的：{"nav_type":"0","competition_id":"0","match_type":"0","page":"1","date":"","limit":"20","check_id":"touristUid_163873678","check_type":"1","lang":"id","client_channel":"52","api_version":"1","client":"h5","timeNowClient":"1745924493092","customer_id":"0","uid":"163873678","token":""}
有直播的：{"nav_type":"5","competition_id":"0","match_type":"0","page":"1","date":"","limit":"20","check_id":"touristUid_163873678","check_type":"1","lang":"id","client_channel":"52","api_version":"1","client":"h5","timeNowClient":"1745948678049","customer_id":"0","uid":"163873678","token":""}
        */
        $params = <<<EOF
nav_type: +GbGVr4E6xYnMRbdfDBiCQ==
competition_id: yFLko2/vaXn4LjiX2bQSqA==
match_type: yFLko2/vaXn4LjiX2bQSqA==
page: XBrwKa8OcE2c/eDTuviUsA==
date: d3i9caJLKda5ogqS1IQ2Fg==
limit: ZYjPspHBgH5lINhbNbxthw==
check_id: nk70DdII4oItgIx15QmcmAvA2Gz3TQCZxhdYHsiNYys=
check_type: XBrwKa8OcE2c/eDTuviUsA==
lang: 8f0kDTpzDo+Khw9ZLKlvjw==
client_channel: MVnDhjj4RSwyuptSUUbT2w==
api_version: XBrwKa8OcE2c/eDTuviUsA==
client: srxv7scadGDvXWAbToX8Ng==
timeNowClient: aSef7dM9LzfBEqgoIISKUA==
customer_id: yFLko2/vaXn4LjiX2bQSqA==
uid: u7mz3KTvOgGjdL3prDbJLQ==
token: d3i9caJLKda5ogqS1IQ2Fg==
EOF;
        $aesk = 'KVksL2jJ6eLOP7cX';
        dd(aes_decrypt("pf2YuMXkHyxu52YPr2Lsfw==",$aesk));
        $array_content = [];
        $params_arr = explode("\n", $params);
        foreach ($params_arr as $param) {
            $param = explode(": ", $param);
            $key = $param[0]??"";
            if ($key){
                $array_content[$key]=aes_decrypt($param[1],$aesk);
            }
        }
        dd(json_encode($array_content));

        $content = <<<EOT
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***********************************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
EOT;
        $jiemi = aes_decrypt($content, $this->key);
//        dd($jiemi);
        $jiemi_json = json_decode($jiemi, true);
//        dd($jiemi_json['data']['list'],array_keys($jiemi_json['data']));
        foreach ($jiemi_json['data']['list'] as $datum){
            dump([
                $datum,
                "staid"=>$datum['status_id'],
                'match_time'=>datetime($datum['match_time']),
            ]);
            dd($datum);
            if ($datum['id']=='5004439')
            {

            }
        }
        dd();

    send_console([
            "type" => "ip_check",
            "data" => [
               'ip'=> "123",
                'type'=>"fail",
            ]
        ],"PING_3");exit;

      $api ='http://127.0.0.1:26131';
        $key = 'Zgc3yI3gKXDHVTkVeoTM25bJVYkbRmDa';
        //LT69feEAGpTb93fHX0dyeEjMkHb8Nfl3
        //Zgc3yI3gKXDHVTkVeoTM25bJVYkbRmDa
        //mZs1mcalgUqnAubimhZ2YyVQruU7DB78
        //xffdFVtSn3BNJBGeNqr6NN19RvGLYKfX
        $bt = new Baota($api, $key);
        $new_path = '/www/wwwroot/marster-web';
        $rewrite =<<<EOF
location ~* (runtime|application)/{
	return 403;
}
location / {
	if (!-e \$request_filename){
		rewrite  ^(.*)$  /index.php?s=$1  last;   break;
	}
}
EOF;

 
        $sites = $bt->all_sites("www.liebao88");
        foreach ($sites as $site){
            $siteName = $site['name'];
            $siteId = $site['id'];
            $path = $site['path'];

            if ($path !='/www/wwwroot/www.liebao88.com'){
                dump("跳过{$siteName} => {$path}");
                continue;
            }
            // if ($siteName == 'www.liebao88.com') continue;
            // $top_host = getTopHost($siteName,false);
            // dump("{$top_host} Start 初始化 DataBase");
            //init_database_config($top_host);
          // $config_name = getTopHost($siteName);            // if (config("database.connections.{$config_name}")){
            //     dump("{$config_name}数据库 init success");
            // }else{
            //     dump("{$config_name}数据库初始化失败");
            //     continue;
            // }

            $vs =$bt->setPHPVersion($siteName, '80');
            dump(["php版本设置："=>$vs['msg']??""]);
            $ps = $bt->setPath($siteId, $new_path);
            dump(["站点目录设置："=> $ps['msg']??""]);
            $rw = $bt->setRewrite($siteName, $rewrite);
            dump(["伪静态设置："=>$rw['msg']??""]);
            
            $url= "https://www.".$siteName;
            $status =  get_http_code($url);
            dump($status,$url);
           // exit;
        }
        exit;
        $start = ms();
        $conf_item = \config("database.connections.mysql");
        $conf_item['database'] = "data_pool";
        $conf_item['fields_cache'] = false;
        $conf = config("database");
        $conf['connections']['data_pool'] = $conf_item;
        Config::set($conf, "database");
             

      #  
  // dump(db("ft_collect_data","data_pool")->where("data_type","<>","live")->where("create_time",">",strtotime("-365 day"))->count());
      dump(db('ft_collect_data','data_pool')->where("data_type","<>","live")->limit(2)->select());
               $end = ms();
        dump($end - $start);
            exit;
            foreach (db('ft_site')->where("assign_data",1)->select() as $k=>$site){
           $data = \db("ft_nav",$site['db_name'])->where("name","CCTV5")->update([
                'status'=>0,
            ]);
            dump($data);
        }
        exit();

       # 循环测试1000次  $this->ck() 取出 1000次的平均值
        $nums = [];
        for ($i = 0; $i < 1000; $i++) {
            $nums[] = $this->ck();
        }
        dump(array_sum($nums) / count($nums));
        exit;
        //指向命令使用wget下载的文件并且重命名
    
//        dump(\think\facade\Db::query("select * from ft_article inner join ft_tags_item on ft_article.id = ft_tags_item.aid inner join ft_tags on ft_tags_item.tag_id = ft_tags.id where ft_tags.title = 'NBA' order by ft_article.id desc limit 0,10;"));
//           dump( \think\facade\Db::query("select aid from ft_tags_item where tag_id = (select id from ft_tags where tag = 'NBA')  order by aid desc limit 0,10"));
//         dump(\think\facade\Db::query("select title from ft_article where id in (select aid from ft_tags_item where tag_id = (select id from ft_tags where title = '意甲') ) order by id desc limit 10,10;"));
        $article_ls = db('ft_article')->field("id,title")->where('tags', "like", '%足球%')->order("id","asc")->paginateX(["page"=>3,'list_rows'=>10]);
        dump($article_ls->items());


//    $this->init_article_tags();
//        compress_img(public_path("static/tp3/images").'score-aa23fas41.png',runtime_path().'score-bg.png',[800,345]);
        $end = ms();
        dump($end - $start);
    }


    function  ck(){
        $s1 = ms();
        $start = strtotime('-30 day');
        $class_w = [];
//        $class_w[] = ["match_cat_en_name", "=", 'zuqiu'];
        $group = db("ft_webcast")
            ->field("match_name as mat_name,match_en_name as en_name,match_cat_en_name,count(*) as num")
//            ->where("start_play", ">=", $start)
//            ->where("start_play", "<=", $start + 86400*3 )
            ->where($class_w)
            ->whereNotNull("match_en_name")
            ->group("match_name")
            ->limit(30)
            ->orderRaw(order_by_match())
            ->order("match_name")
            ->select()
            ->toArray();
        dump(count($group));
        dump(ms($s1));
        return ms($s1);
    }


    function tengsite()
    {
        $key = '';//LT69feEAGpTb93fHX0dyeEjMkHb8Nfl3
        $bt = new Baota('', $key);
        $sites = $bt->get_sites(14);
        file_put_contents(root_path() . "/site.txt", implode("\n", array_column($sites, 'name')));
        $site_union_array = [];
        $webname = 'test.com';
        $add_to_webname = '127.0.0.1';
        foreach ($sites as $site) {
            $site_name = $site['name'];
            $domain = $this->get_domain($site_name);
            if ($site_name == "backend.tocall.cn") continue;

            if (!in_array($domain, $site_union_array)) {
                $this->show_log($bt->del_domain(14, "*.{$domain}", $webname));
                $this->show_log($bt->del_domain(14, "www.{$domain}", $webname));
                $this->show_log($bt->del_domain(14, $domain, $webname));

                $this->show_log($domain . ":" . $bt->add_domain(13, "$domain,*.{$domain}", $add_to_webname)[0]['msg']);
                $site_union_array[] = $domain;
//                $site_union_array[] = "*.{$domain}";
            }
        }
//        $site_union_str = implode(',',$site_union_array);
//        dump($site_union_str);
//   dump($bt->add_domain(14,$site_union_str,'dongqiudi.com'));
//        dump($bt->del_domain(14,'www.abc.com','dongqiudi.com'));
    }

    function get_domain($host)
    {
        $data = explode('.', $host);
        $n = count($data);
        $preg = '/[\w].+\.(com|net|org|gov|edu)\.cn$/';
        if (($n > 2) && preg_match($preg, $host)) {
            $host = $data[$n - 3] . '.' . $data[$n - 2] . '.' . $data[$n - 1];
        } else {
            $host = $data[$n - 2] . '.' . $data[$n - 1];
        }
        return $host;
    }


       function init_article_tags()
    {
        $limit=100;
       # \think\facade\Db::execute("drop index ft_tags_tag_uindex on ft_tags;");
       # \think\facade\Db::execute("create index ft_tags_tag_index on ft_tags (tag);");
        $id = 0;

        $count = db('ft_article')->where("id",">=",$id)->field("id,title")->count();
        $page_count = ceil($count / $limit);
        for ($i = 0; $i < $page_count; $i++){
            $article_ls = db('ft_article')->where("id",">=",$id)->order("id","asc")->paginateX(["page"=>$i,'list_rows'=>$limit]);
            if (!$article_ls->items()) {
                exit("ok");
            }
            foreach ($article_ls->items() as $article) {
                dump("id:[{$article['id']}] title:" . $article['title']);
                $tags_arr = json_decode($article['tags'], true);
                $tag_ids = $this->check_tags_add($tags_arr);
//                db('ft_tags_item')->where('aid', $article['id'])->delete();
//                db('ft_tags_item')->insertAll(array_map(function ($tag_id) use ($article) {
//                    return ['aid' => $article['id'], 'tag_id' => $tag_id];
//                }, $tag_ids));
            }
        }
        dump("ok");
    }

    function check_tags_add($tags_arr = [])
    {
        $tag_ids = [];
        if (!$tags_arr){
            return $tag_ids;
        }
        foreach ($tags_arr as $tag) {
            if (empty($tag) or in_str($tag['name'],['topic'])) continue;

            $tag_id = db("ft_tags")->where('title', $tag['name'])->value("id");
            if (!$tag_id) {
                $tag_id = db("ft_tags")->insertGetId(['title' => $tag['name'], 'tag' => $tag['code'], 'create_time' => time()]);
            }
            $tag_ids[] = $tag_id;
        }
        return $tag_ids;
    }


    function show_log($str)
    {
        dump(date("[Y-m-d H:i:s]") . ((is_object($str) or is_array($str)) ? json_encode($str, JSON_UNESCAPED_UNICODE) : $str));
    }

    // curl上传文件到指定服务器
    private function  curl_upload($url, $file_path, $post_data = [])
    {
        $ch = curl_init();
        $post_data['file'] = new \CURLFile($file_path);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1); //设置为POST方式
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $result = curl_exec($ch);
        curl_close($ch);
        return json_decode($result, true);
    }

    // 接收上传的文件
    public function upload()
    {
        $file = request()->file('file');
        //只允许jpg
        if ($file && $file->getExtension() != 'jpg') {
            echo '只允许jpg';
            return;
        }

    }
}

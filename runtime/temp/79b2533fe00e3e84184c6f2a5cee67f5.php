<?php /*a:5:{s:70:"/Users/<USER>/PhpstormProjects/id_project/view/tpls/simple/pc/live.html";i:1748707680;s:74:"/Users/<USER>/PhpstormProjects/id_project/view/tpls/simple/public/base.html";i:1748543697;s:79:"/Users/<USER>/PhpstormProjects/id_project/view/tpls/simple/public/pc_header.html";i:1748533575;s:79:"/Users/<USER>/PhpstormProjects/id_project/view/tpls/simple/public/live-item.html";i:1748663982;s:79:"/Users/<USER>/PhpstormProjects/id_project/view/tpls/simple/public/pc_footer.html";i:1748542573;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta name="keywords" content="<?php echo htmlentities((string) $base['keywords']); ?>"/>
    <meta name="Description" content="<?php echo htmlentities((string) $base['description']); ?>"/>
    <meta property="og:title" content="<?php echo htmlentities((string) $base['title']); ?>">
    <meta property="og:site_name" content="<?php echo htmlentities((string) $site_name); ?>">
    <meta property="og:description" content="<?php echo htmlentities((string) $base['description']); ?>">
    
    <title><?php echo htmlentities((string) $base['title']); ?></title>
    <link rel="stylesheet" href="/static/iconfont/iconfont.css">
    <script src="/static/js/tailwindcss-3.4.16.min.js"></script>
    <script src="/static/js/jquery-1.10.2.min.js"></script>
    <link rel="shortcut icon" href="<?php echo htmlentities((string) $favicon_ico); ?>" type="image/x-icon">
    
    <style>
        /* 自定义滚动条样式 */
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        /* 加载更多动画 */
        .loader {
            border-top-color: #3498db;
            -webkit-animation: spinner 1.5s linear infinite;
            animation: spinner 1.5s linear infinite;
        }
        @-webkit-keyframes spinner {
            0% { -webkit-transform: rotate(0deg); }
            100% { -webkit-transform: rotate(360deg); }
        }
        @keyframes spinner {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* PC端直播卡片样式 */
        .live-item {
            border-left: 4px solid #e53e3e;
            transition: all 0.3s ease;
        }

        .live-item:hover {
            transform: translateY(-2px);
            border-left-color: #3182ce;
        }

        .live-item a:hover {
            text-decoration: none;
        }
    </style>
</head>
<body class="bg-gray-100">
<div class="max-w-7xl mx-auto bg-white min-h-screen flex flex-col">
    <header class="bg-white shadow-md py-4">
    <div class="container mx-auto px-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-6">
                <div class="logo">
                    <img src="https://jala-live.com/uploads/20250228/6412b1aa286be63cf3d55227a671b4c2.png" alt="logo" class="h-12">
                </div>
                <!-- 桌面端导航菜单 -->
                <nav class="hidden md:flex space-x-6">
                    <?php $pathname=''; if(is_array($nav) || $nav instanceof \think\Collection || $nav instanceof \think\Paginator): $i = 0; $__LIST__ = $nav;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$n): $mod = ($i % 2 );++$i; $is_active = pathEq(input('path'),$n['path'])?'text-red-500':'text-gray-800';  $is_active&&$pathname=$n['name'];  ?>
                    <a href="<?php echo htmlentities((string) $n['path']); ?>" class="<?php echo htmlentities((string) $is_active); ?> hover:text-red-500 font-medium" target="<?php echo htmlentities((string) $n['target']); ?>"><?php echo htmlentities((string) $n['name']); ?></a>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                </nav>
            </div>
            <div class="flex items-center space-x-4">
                <div class="relative hidden md:block">
                    <input type="text" placeholder="搜索比赛、队伍" class="bg-gray-100 rounded-full py-2 px-4 pl-10 focus:outline-none focus:ring-2 focus:ring-red-500 w-64">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 absolute left-3 top-2.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </div>
                <button class="md:hidden p-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </button>
                <button class="p-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                    </svg>
                </button>
                <a href="#" class="hidden md:block bg-red-500 text-white px-4 py-2 rounded-full hover:bg-red-600 transition">登录/注册</a>
            </div>
        </div>
    </div>
</header>
    
<main class="flex-grow py-6 px-4 overflow-y-auto container mx-auto" id="live-container">
    <!-- 轮播图 -->
    <div class="rounded-lg overflow-hidden mb-6 relative">
        <div class="nav-scroll-container   scrollbar-hide">
            <ul class="flex whitespace-nowrap px-4 space-x-6">
                <?php if(is_array($match_class) || $match_class instanceof \think\Collection || $match_class instanceof \think\Paginator): $i = 0; $__LIST__ = $match_class;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$m): $mod = ($i % 2 );++$i;
                $is_select = 'text-gray-600';
                if($m['is_select']){
                $is_select = 'border-b-2 border-red-500 pb-1';
                $m['en_name']!='all' && $mat_name = $m['mat_name'];
                }
                 ?>
                <li class="font-medium <?php echo $is_select; ?>"><a href="<?php echo htmlentities((string) $path); ?>/<?php echo htmlentities((string) $m['en_name']); ?>/" ><?php echo htmlentities((string) $m['mat_name']); ?></a></li>
                <?php endforeach; endif; else: echo "" ;endif; ?>
            </ul>
        </div>
    </div>

    <div class="container mx-auto px-4">
        <div class="flex flex-col md:flex-row gap-6" id="main-content">
            <div id="live-content" class="flex-1 w-full md:w-2/3">
                <!--直播推荐-->
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl md:text-2xl font-bold">热门直播</h2>
                    <div class="flex space-x-2 overflow-x-auto py-2 scrollbar-hide" id="tabs-title">
<!--                        <span class="px-3 py-1 bg-red-500 text-white rounded-full text-sm cursor-pointer">热门</span>-->
<!--                        <span class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm cursor-pointer hover:bg-gray-200">足球</span>-->
<!--                        <span class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm cursor-pointer hover:bg-gray-200">篮球</span>-->
<!--                        <span class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm cursor-pointer hover:bg-gray-200">电竞</span>-->

                        <?php if(is_array($match_class) || $match_class instanceof \think\Collection || $match_class instanceof \think\Paginator): $i = 0; $__LIST__ = $match_class;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$m): $mod = ($i % 2 );++$i;
                        $is_select = 'bg-gray-100 text-gray-600';
                        if($m['is_select']){
                        $is_select = 'bg-red-500 text-white';
                        $m['en_name']!='all' && $mat_name = $m['mat_name'];
                        }
                         ?>
                        <a class="px-3 py-1 cursor-pointer <?php echo $is_select; ?> text-gray-600 rounded-full text-sm" href="<?php echo htmlentities((string) $path); ?>/<?php echo htmlentities((string) $m['en_name']); ?>/"> <?php echo htmlentities((string) $m['mat_name']); ?> </a>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </div>
                </div>
                <!-- 直播列表 -->
                <div class="grid grid-cols-1 gap-4" id="tabs-content">

                    <div class="live-item active">
                        <?php if(is_array($list) || $list instanceof \think\Collection || $list instanceof \think\Paginator): $i = 0; $__LIST__ = $list;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$d): $mod = ($i % 2 );++$i;?>
                        <div class="text-center text-xs text-gray-400 mb-2">   <?php echo htmlentities((string) $d['day']); ?> <?php echo htmlentities((string) $d['week']); ?> </div>
                        <?php if(is_array($d['list']) || $d['list'] instanceof \think\Collection || $d['list'] instanceof \think\Paginator): $i = 0; $__LIST__ = $d['list'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;$ext = $vo['is_play']<10?'gif':'png';     ?>
<div class="bg-white rounded-lg overflow-hidden shadow-sm p-4 mb-1">
  <div class="flex items-center justify-between mb-2">
    <a href="<?php echo htmlentities((string) $vo['match_path']); ?>"> <img src="/static/img/<?php echo htmlentities((string) $vo['match_cat_en_name']); ?>.<?php echo htmlentities((string) $ext); ?>" alt="logo" class="h-5"></a>
    <a href="<?php echo htmlentities((string) $vo['live_path']); ?>" class="text-xs text-gray-500"><?php echo htmlentities((string) $vo['match_name']); ?></a>
    <?php if($vo['is_play']): ?>
    <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">LIVE</span>
    <?php else: ?>
    <span class="bg-gray-100 text-white text-xs px-2 py-1 rounded-full">LIVE</span>
    <?php endif; ?>
  </div>
  <div class="flex items-center justify-between">
    <!-- 主队信息 -->
    <div class="flex flex-col items-center w-1/3">
      <a href="<?php echo htmlentities((string) $vo['detail_path']); ?>">
        <img src="<?php echo htmlentities((string) $vo['team_A_logo']); ?>" alt="<?php echo htmlentities((string) $vo['team_B_name']); ?>" class="w-12 h-12 object-cover rounded-full mb-2 items-center">
      </a>
      <span class="text-xs font-extralight text-center"><?php echo htmlentities((string) $vo['team_A_name']); ?></span>
    </div>
    <!-- 比分和时间 -->
    <div class="flex flex-col items-center w-1/3">
      <div class="flex items-center space-x-3 text-lg font-bold mb-1">
        <span><?php echo htmlentities((string) $vo['fs_A']); ?></span>
        <span class="text-gray-400">-</span>
        <span><?php echo htmlentities((string) $vo['fs_B']); ?></span>
      </div>
      <span class="text-xs text-gray-500"><?php echo htmlentities((string) $vo['start']); ?></span>
      <span class="text-xs text-red-500 mt-1"><?php echo htmlentities((string) $vo['live_status']); ?></span>
    </div>
    <!-- 客队信息 -->
    <div class="flex flex-col items-center w-1/3">
      <a href="<?php echo htmlentities((string) $vo['detail_path']); ?>?tpl=simple">
        <img src="<?php echo htmlentities((string) $vo['team_B_logo']); ?>"
             alt="<?php echo htmlentities((string) $vo['team_B_name']); ?>"
             class="w-12 h-12 object-cover rounded-full mb-2"> </a>
        <span class="text-xs font-extralight text-center"><?php echo htmlentities((string) $vo['team_B_name']); ?></span>

    </div>
  </div>
</div>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                        <!-- 加载更多 -->
                        <div class="flex justify-center items-center py-4 hidden">
                            <div class="loader ease-linear rounded-full border-4 border-t-4 border-gray-200 h-8 w-8"></div>
                            <span class="ml-2 text-gray-500"><?php echo lang("loading"); ?></span>
                        </div>
                    </div>

                </div>


            </div>
            <aside id="article-rec" class="w-full md:w-1/3">
                <!--推荐文章-->

                <div class="article-hot">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl md:text-2xl font-bold">推荐文章</h2>
                        <a href="#" class="text-sm text-gray-500 hover:underline">更多</a>
                    </div>

                    <div class="grid grid-cols-1 gap-4 md:gap-6" id="article-list">
                        <div class="bg-white rounded-lg overflow-hidden shadow-sm p-4">
                            <div class="flex space-x-3">
                                <img src="https://jala-live.com/static/img/20250224/ea1fc07b2b8f79cd543e4b0761f7b7eb.jpg" alt="文章封面" class="w-24 h-24 object-cover rounded">
                                <div class="flex-1">
                                    <h3 class="text-base font-medium text-gray-900 line-clamp-2">NBA：湖人击败勇士，詹姆斯砍下三双</h3>
                                    <p class="text-gray-500 text-sm mt-1 line-clamp-2">詹姆斯贡献28分12篮板10助攻，带领球队获胜</p>
                                    <div class="flex items-center justify-between mt-2">
                                        <span class="text-xs text-gray-400">3小时前</span>
                                        <span class="text-xs text-red-500">阅读 1.8万</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

            </aside>
        </div>
    </div>

</main>

    <footer class="bg-white shadow-md py-8 border-t">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
                <img src="https://jala-live.com/uploads/20250228/6412b1aa286be63cf3d55227a671b4c2.png" alt="logo" class="h-10 mb-4">
                <p class="text-gray-600 text-sm"><?php echo htmlentities((string) $site_name); ?>  是您观看体育赛事的最佳选择，提供高清直播和赛事回放。</p>
            </div>
            <div>
                <h4 class="font-bold text-gray-800 mb-4">快速链接</h4>
                <ul class="space-y-2">
                    <li><a href="#" class="text-gray-600 hover:text-red-500">首页</a></li>
                    <li><a href="#" class="text-gray-600 hover:text-red-500">直播</a></li>
                    <li><a href="#" class="text-gray-600 hover:text-red-500">赛程</a></li>
                    <li><a href="#" class="text-gray-600 hover:text-red-500">新闻</a></li>
                </ul>
            </div>
            <div>
                <h4 class="font-bold text-gray-800 mb-4">体育项目</h4>
                <ul class="space-y-2">
                    <li><a href="#" class="text-gray-600 hover:text-red-500">足球</a></li>
                    <li><a href="#" class="text-gray-600 hover:text-red-500">篮球</a></li>
                    <li><a href="#" class="text-gray-600 hover:text-red-500">电子竞技</a></li>
                    <li><a href="#" class="text-gray-600 hover:text-red-500">其他体育</a></li>
                </ul>
            </div>
        </div>
        <div class="border-t border-gray-200 mt-8 pt-6 text-center">
            <p class="text-gray-500 text-sm">© 2025 <?php echo htmlentities((string) $site_name); ?> . 保留所有权利。</p>
        </div>
    </div>
</footer>
</div>

<script>
    // 选项卡
    const tabTitles = $('#tabs-title span');
    const tabContents = $('#live-content .live-item');
    // Initialize - hide all content except first one
    tabContents.not(':first').addClass('hidden');
    // Add click event handler to tab titles
    tabTitles.click(function() {
        // Remove active class from all tabs
        tabTitles.removeClass('bg-red-500 text-white').addClass('bg-gray-100 text-gray-600');
        // Add active class to clicked tab
        $(this).removeClass('bg-gray-100 text-gray-600').addClass('bg-red-500 text-white');
        // Get the index of clicked tab
        const index = $(this).index();
        // Hide all content
        tabContents.addClass('hidden');
        console.warn(tabContents.eq(index))
        // Show corresponding content
        tabContents.eq(index).removeClass('hidden');
    });
</script>

<?php echo $site_tj; ?>
</body>
</html>

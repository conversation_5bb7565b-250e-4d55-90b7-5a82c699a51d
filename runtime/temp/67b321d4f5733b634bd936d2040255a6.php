<?php /*a:5:{s:74:"/Users/<USER>/PhpstormProjects/id_project/view/tpls/simple/mobile/play.html";i:1746039382;s:81:"/Users/<USER>/PhpstormProjects/id_project/view/tpls/simple/public/mobile_base.html";i:1747223771;s:81:"/Users/<USER>/PhpstormProjects/id_project/view/tpls/simple/public/header-back.html";i:1745302802;s:77:"/Users/<USER>/PhpstormProjects/id_project/view/tpls/simple/public/comment.html";i:1745923068;s:82:"/Users/<USER>/PhpstormProjects/id_project/view/tpls/simple/public/article_item.html";i:1748598250;}*/ ?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1.0, maximum-scale=1, user-scalable=no">
    <title><?php echo htmlentities((string) $base['title']); ?></title>
    <meta name="keywords" content="<?php echo htmlentities((string) $base['keywords']); ?>"/>
    <meta name="Description" content="<?php echo htmlentities((string) $base['description']); ?>"/>
    <script src="/static/js/tailwindcss-3.4.16.min.js"></script>
    <link rel="stylesheet" href="/static/iconfont/iconfont.css">
    <link rel="shortcut icon" href="<?php echo htmlentities((string) $favicon_ico); ?>" type="image/x-icon">
    <style>
        /* 自定义滚动条样式 */
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        /* 加载更多动画 */
        .loader {
            border-top-color: #3498db;
            -webkit-animation: spinner 1.5s linear infinite;
            animation: spinner 1.5s linear infinite;
        }
        .footer-shadow{
            box-shadow: rgba(51 ,51 ,51 ,0.1) 18px 3px 10px 10px;
        }
        @-webkit-keyframes spinner {
            0% { -webkit-transform: rotate(0deg); }
            100% { -webkit-transform: rotate(360deg); }
        }
        @keyframes spinner {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        [v-cloak]{
            display: none;
        }
    </style>
    
<link rel="stylesheet" href="/static/css/video-js.min.css">
<link rel="stylesheet" href="/static/css/vant.min.css">

</head>
<body>
<div class="max-w-md mx-auto bg-white min-h-screen flex flex-col pb-14" id="app">
<!-- 顶部导航 -->

<header class="bg-white shadow-md p-4">
    <div class="flex justify-between items-center">
        <div class="flex items-center space-x-2">
            <button class="p-1" onclick="window.history.back()">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </button>
            <h1 class="text-white font-bold">
                JALA Live
            </h1>
            <div class="logo">
                <a href="/"><img src="<?php echo htmlentities((string) $wap_logo); ?>" alt="logo" class="h-10"></a>
            </div>
        </div>
        <div class="flex items-center space-x-2">
<!--            <button class="p-1">-->
<!--                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">-->
<!--                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />-->
<!--                </svg>-->
<!--            </button>-->
            <button class="p-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                </svg>
            </button>
        </div>
    </div>
</header>




<div class="bg-gradient-to-r from-red-500 to-red-600 py-2 px-4">
    <div class="flex justify-around text-white text-sm">
        <div class="flex flex-col items-center">
            <span class="font-bold">328</span>
            <span class="text-xs">Session</span>
        </div>
        <div class="flex flex-col items-center">
            <span class="font-bold">12.5k</span>
            <span class="text-xs">Number of viewers</span>
        </div>
        <div class="flex flex-col items-center">
            <span class="font-bold">96%</span>
            <span class="text-xs">Applause Rate</span>
        </div>
    </div>
</div>
<!-- 主要内容区域 -->
<main class="flex-grow px-3 overflow-y-auto" id="live-container">

    <!-- 直播列表 -->
    <div class="grid grid-cols-1 gap-3" id="live-info">
        <!-- 比赛信息展示 -->
        <div class="bg-white rounded-lg   p-4">
            <div class="text-center mb-4">
                <span class="text-sm text-gray-500"><?php echo htmlentities((string) $data['match_name']); ?></span>
            </div>
            <!-- 队伍信息和比分 -->
            <div class="flex justify-between items-center mb-6">
                <div class="flex flex-col items-center w-1/3">
                    <img src="<?php echo htmlentities((string) $data['team_A_logo']); ?>" alt="<?php echo htmlentities((string) $data['team_A_name']); ?>" class="w-16 h-16 object-contain mb-2 rounded-lg">
                    <span class="text-sm font-medium text-center"><?php echo htmlentities((string) $data['team_A_name']); ?></span>
                </div>

                <div class="flex flex-col items-center w-1/3">
                    <div class="text-3xl font-bold mb-2">
                        <span><?php echo htmlentities((string) $data['fs_A']); ?></span>
                        <span class="mx-2">-</span>
                        <span><?php echo htmlentities((string) $data['fs_B']); ?></span>
                    </div>
                    <div class="flex flex-col items-center">
                        <span class="text-red-500 text-sm mb-1"><?php echo htmlentities((string) $data['live_status']); ?></span>
                        <span class="text-gray-500 text-sm"><?php echo htmlentities((string) $data['start']); ?></span>
                    </div>
                </div>

                <div class="flex flex-col items-center w-1/3">
                    <img src="<?php echo htmlentities((string) $data['team_B_logo']); ?>" alt="<?php echo htmlentities((string) $data['team_B_name']); ?>" class="w-16 h-16 object-contain mb-2 rounded-lg">
                    <span class="text-sm font-medium text-center"><?php echo htmlentities((string) $data['team_B_name']); ?></span>
                </div>
            </div>

            <!-- 直播视频播放器 -->
            <div class="aspect-video rounded-lg overflow-hidden">
                <video id="live-player" class="w-full h-full video-js vjs-default-skin vjs-big-play-centered" controls>
                </video>
            </div>
            <!-- 在比赛信息下方添加数据统计 -->
            <div class="grid grid-cols-3 gap-4 my-4">
                <div class="bg-gray-50 rounded-lg p-3 text-center">
                    <div class="text-2xl font-bold text-red-500">65%</div>
                    <div class="text-xs text-gray-600">Penguasaan Bola</div>
                </div>
                <div class="bg-gray-50 rounded-lg p-3 text-center">
                    <div class="text-2xl font-bold text-red-500">12</div>
                    <div class="text-xs text-gray-600">Jumlah bidikan</div>
                </div>
                <div class="bg-gray-50 rounded-lg p-3 text-center">
                    <div class="text-2xl font-bold text-red-500">5</div>
                    <div class="text-xs text-gray-600">Tendangan Sudut</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 评论 -->
    <div class="max-w-4xl mx-auto py-8" id="comment" ref="comment-box" data-id="<?php echo htmlentities((string) $data['id']); ?>" data-from="<?php echo htmlentities((string) $detail_type); ?>" v-cloak>

  <div class="bg-white rounded-lg p-3">
    <h2 class="text-2xl font-bold mb-6 text-gray-800"><?php echo lang('comment'); ?></h2>

    <!-- Comment Form -->
    <div class="mb-8">
      <textarea v-model="newComment"
              class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
              rows="3" placeholder="<?php echo lang('comment_say'); ?>" ></textarea>
      <!-- Verification Code Section -->
      <div class="mt-4 flex items-center space-x-4">
        <div class="flex-1">
          <input v-model="verificationCode" type="text"
                  class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                  placeholder="<?php echo lang('input_code'); ?>" maxlength="4"  />
        </div>
        <div class="bg-gray-100 rounded-lg overflow-hidden tracking-wider cursor-pointer select-none h-10" @click="generateVerificationCode">
          <van-image :src="codeSrc" width="120px" class="h-10 rounded-lg">
            <template v-slot:loading> <van-loading type="spinner" size="20" /> </template>
          </van-image>
        </div>
      </div>

      <van-button @click="submitComment" :loading="btn_loading" type="danger"  class="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg  transition-colors w-full" ><?php echo lang('send_comment'); ?></van-button>

      <div v-if="verificationError" class="mt-2 text-red-500 text-sm">
        <?php echo lang('code error'); ?>
      </div>
    </div>
    <!-- Comments List -->
    <div class="space-y-6">
      <div v-for="(comment, index) in comments" :key="index" class="border-b pb-4">
        <div class="flex items-start space-x-4">
          <div class="flex-shrink-0">
            <img :src="avatar" class="w-10 h-10 rounded-full"  alt="用户头像" />
          </div>
          <div class="flex-1">
            <div class="flex items-center justify-between">
              <h3 class="font-medium text-gray-900">{{ nickname }}</h3>
              <span class="text-sm text-gray-500">{{ comment.created_at }}</span>
            </div>
            <p class="mt-1 text-gray-700">{{ comment.content }}</p>
            <div class="mt-2 flex items-center text-sm text-gray-500">
              <span class="mr-4">IP: {{ comment.ip_addr }}</span>
              <div class="flex items-center ml-auto">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
                <span>{{ comment.likes }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <van-empty v-if="comments.length<=0" image="/static/img/custom-empty-image.png" image-size="80" description="<?php echo lang('not have comment'); ?>" />
    </div>
  </div>

</div>



    <!-- 相关推荐 -->
    <div class="grid grid-cols-1 gap-4 mt-6" id="live-more">
        <h2 class="text-lg font-bold mb-2"><?php echo lang('recommend'); ?></h2>
        <!-- 推荐文章项 -->
        <?php if(is_array($wrc_news_list) || $wrc_news_list instanceof \think\Collection || $wrc_news_list instanceof \think\Paginator): $i = 0; $__LIST__ = $wrc_news_list;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$li): $mod = ($i % 2 );++$i;?>
            <div class="bg-white rounded-lg p-3 article-item">
    <div class="flex space-x-3">
        <img src="<?php echo getImgUrl($li['thumb']); ?>" alt="<?php echo htmlentities((string) $li['title']); ?>"  class="w-28 h-28 object-cover rounded-lg flex-shrink-0" />
        <div class="flex flex-col justify-between flex-grow overflow-hidden">
            <div>
                <a href="<?php echo auto_url($li); ?>?tpl=simple">
                    <h3 class="text-base font-semibold mb-2 line-clamp-2 "><?php echo htmlentities((string) $li['title']); ?></h3>
                </a>
                    <p class="text-sm text-gray-600 line-clamp-2 overflow-hidden"> <?php echo get_text($li['content']); ?> </p>
            </div>
            <div class="flex items-center justify-between text-xs text-gray-500">
                <div class="flex items-center space-x-2">
                    <span class="text-xs font-thin"><?php echo htmlentities((string) $li['match_id']); ?></span>
                    <span>•</span>
                </div>
                <div class="flex items-center space-x-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                   </svg>
                    <span><?php echo datetime($li['create_time'],"Y/m/d"); ?></span>
                </div>
            </div>
        </div>
    </div>
</div>
        <?php endforeach; endif; else: echo "" ;endif; ?>
    </div>
    <!-- 加载更多 -->
    <div id="loader" class="flex justify-center items-center py-4 hidden">
        <div class="loader ease-linear rounded-full border-4 border-t-4 border-gray-200 h-8 w-8"></div>
        <span class="ml-2 text-gray-500">加载中...</span>
    </div>
</main>

<footer class="bg-white footer-shadow py-2 fixed bottom-0 left-0 right-0 z-10" id="footer-nav">
    <div class="max-w-md mx-auto grid grid-cols-4 gap-1 footer_nav">
        <div class="flex flex-col items-center text-gray-500 index">
            <span class="iconfont icon-home text-xl"></span>
            <a href="/?tpl=simple" class="text-xs mt-1"><?php echo lang("go_index"); ?></a>
        </div>
        <div class="flex flex-col items-center text-gray-500 football">
            <span class="iconfont icon-football text-xl"></span>
            <a href="/Siaran-Langsung-Sepak-Bola/?tpl=simple" class="text-xs mt-1"><?php echo lang("football"); ?></a>
        </div>
        <div class="flex flex-col items-center text-gray-500 basketball">
            <span class="iconfont icon-basketball text-xl"></span>
            <a href="/Siaran-Langsung-Bola-Basket/?tpl=simple" class="text-xs mt-1"><?php echo lang("basketball"); ?></a>
        </div>
        <div class="flex flex-col items-center text-gray-500 news">
            <span class="iconfont icon-news text-xl"></span>
            <a href="/informasi-publik/?tpl=simple" class="text-xs mt-1"><?php echo lang("news"); ?></a>
        </div>
    </div>
</footer>
</div>
<script src="/static/js/jquery-1.10.2.min.js"></script>
<script src="/static/js/vue3.min.js"></script>
<script src="/static/js/vant.min.js"></script>

<script src="/static/js/video.min.js"></script>
<script src="/static/js/crypto-js.min.js"></script>

<script src="/static/js/comment.base.js?v=<?php echo time(); ?>"> </script>
<script src="/static/js/live-play.js?v=<?php echo time(); ?>"></script>
<script src="/static/js/hls.min.js"></script>
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/videojs-contrib-hls/5.15.0/videojs-contrib-hls.min.js"></script>-->
<!--<script src="https://unpkg.com/videojs-flash/dist/videojs-flash.js"></script>-->
<!--<script src="https://unpkg.com/videojs-contrib-hls/dist/videojs-contrib-hls.js"></script>-->
<script>
playApp.initApp('<?php echo htmlentities((string) $data['union_str']); ?>');
</script>

<script>
let curr_class = "<?php echo htmlentities((string) $template); ?>";
let footer = $("footer .footer_nav");
footer.find(".items-center").removeClass("text-gray-500");
footer.find(".items-center").each(function(i,e){
    let href = $(e).find('a').attr('href');
    if($(e).hasClass(curr_class)){
        $(e).addClass("text-red-500");
    }else{
        $(e).removeClass("text-gray-500");
    }
    $(e).on('touchend click', function(e2) {
        e2.preventDefault();
        console.log('触发点击或触摸');
        location = href;
    });
});


$(".article-item").click(function(){
    vant.showLoadingToast({
        message: 'Memuat...',
        forbidClick: true,
    });
    location=$(this).find('a').attr('href')
})
</script>
<?php echo $site_tj; ?>
</body>
</html>
